// Estado da aplicação
let conversationsClient = null;
let activeConversation = null;
let currentUserIdentity = null;
let typingTimeout = null;
let autoRefreshLogs = false;
let autoRefreshInterval = null;
let conversationsRefreshInterval = null;
let activeConversations = new Map();

// Elementos DOM
const elements = {
    userIdentity: document.getElementById('userIdentity'),
    connectBtn: document.getElementById('connectBtn'),
    connectionStatus: document.getElementById('connectionStatus'),
    whatsappStatus: document.getElementById('whatsappStatus'),
    webhookStatus: document.getElementById('webhookStatus'),
    checkWebhooksBtn: document.getElementById('checkWebhooksBtn'),
    conversationsList: document.getElementById('conversationsList'),
    conversationsCount: document.getElementById('conversationsCount'),
    refreshConversationsBtn: document.getElementById('refreshConversationsBtn'),
    closeAllConversationsBtn: document.getElementById('closeAllConversationsBtn'),
    testWhatsAppBtn: document.getElementById('testWhatsAppBtn'),
    chatHeader: document.getElementById('chatHeader'),
    activeChatTitle: document.getElementById('activeChatTitle'),
    activeChatDetails: document.getElementById('activeChatDetails'),
    closeChatBtn: document.getElementById('closeChatBtn'),
    messagesContainer: document.getElementById('messagesContainer'),
    typingIndicator: document.getElementById('typingIndicator'),
    messageInput: document.getElementById('messageInput'),
    sendBtn: document.getElementById('sendBtn'),
    logsContainer: document.getElementById('logsContainer'),
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    refreshLogsBtn: document.getElementById('refreshLogsBtn'),
    toggleAutoRefreshBtn: document.getElementById('toggleAutoRefreshBtn')
};

// Testar WhatsApp diretamente
async function testWhatsApp() {
    const number = prompt('Digite o número para teste (ex: +5551993590210):', '+5551993590210');
    if (!number) return;

    try {
        elements.testWhatsAppBtn.disabled = true;
        elements.testWhatsAppBtn.textContent = '⏳ Testando...';
        
        addLog(`Testando WhatsApp para: ${number}`, 'info');
        
        const response = await fetch(`/api/conversations/test-whatsapp?number=${encodeURIComponent(number)}`);
        
        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        
        addLog(`✅ Teste concluído: ${data.summary.successful}/${data.summary.total} senders funcionando`, 'success');
        
        data.results.forEach(result => {
            if (result.success) {
                addLog(`✅ ${result.sender}: Mensagem enviada (${result.messageSid})`, 'success');
            } else {
                addLog(`❌ ${result.sender}: ${result.error}`, 'error');
            }
        });
        
        if (data.summary.successful > 0) {
            addLog(`🎉 Sistema WhatsApp funcionando! Verifique seu telefone.`, 'success');
        } else {
            addLog(`⚠️ Nenhum sender funcionou. Verifique configuração.`, 'warning');
        }

    } catch (error) {
        console.error('Erro no teste WhatsApp:', error);
        addLog(`Erro no teste WhatsApp: ${error.message}`, 'error');
    } finally {
        elements.testWhatsAppBtn.disabled = false;
        elements.testWhatsAppBtn.textContent = '📱 Testar WhatsApp';
    }
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    addLog('Sistema de chat reativo iniciado', 'success');
    checkWebhookStatus();
});

// Event Listeners
function setupEventListeners() {
    elements.connectBtn.addEventListener('click', handleConnect);
    elements.checkWebhooksBtn.addEventListener('click', checkWebhookStatus);
    elements.refreshConversationsBtn.addEventListener('click', refreshConversations);
    elements.closeAllConversationsBtn.addEventListener('click', closeAllConversations);
    elements.testWhatsAppBtn.addEventListener('click', testWhatsApp);
    elements.closeChatBtn.addEventListener('click', closeActiveChat);
    elements.sendBtn.addEventListener('click', handleSendMessage);
    elements.messageInput.addEventListener('keypress', handleMessageKeyPress);
    elements.messageInput.addEventListener('input', handleTyping);
    elements.clearLogsBtn.addEventListener('click', clearLogs);
    elements.refreshLogsBtn.addEventListener('click', refreshServerLogs);
    elements.toggleAutoRefreshBtn.addEventListener('click', toggleAutoRefresh);
}

// Conectar ao Twilio Conversations como operador
async function handleConnect() {
    const identity = elements.userIdentity.value.trim();

    if (!identity) {
        addLog('Por favor, insira uma identidade de operador', 'error');
        return;
    }

    try {
        updateConnectionStatus('connecting', 'Conectando...');
        elements.connectBtn.disabled = true;

        addLog(`Conectando como operador: ${identity}`);

        // Obter token de acesso
        const response = await fetch('/api/auth/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identity })
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog('Token obtido com sucesso', 'success');

        // Inicializar cliente Conversations
        conversationsClient = new Twilio.Conversations.Client(data.token);
        currentUserIdentity = identity;

        // Event listeners do cliente
        conversationsClient.on('connectionStateChanged', handleConnectionStateChanged);
        conversationsClient.on('conversationJoined', handleConversationJoined);
        conversationsClient.on('conversationLeft', handleConversationLeft);

        addLog('Cliente Conversations inicializado', 'success');
        updateConnectionStatus('online', `Operador: ${identity}`);

        elements.userIdentity.disabled = true;
        elements.connectBtn.textContent = 'Conectado';

        // Iniciar monitoramento de conversas
        startConversationsMonitoring();

        addLog('Sistema reativo ativo - Aguardando mensagens dos usuários', 'success');

    } catch (error) {
        console.error('Erro ao conectar:', error);
        addLog(`Erro ao conectar: ${error.message}`, 'error');
        updateConnectionStatus('offline', 'Erro na conexão');
        elements.connectBtn.disabled = false;
    }
}

// Verificar status dos webhooks
async function checkWebhookStatus() {
    try {
        elements.checkWebhooksBtn.disabled = true;
        updateWebhookStatus('connecting', 'Verificando...');

        const response = await fetch('/webhooks/test');
        if (response.ok) {
            updateWebhookStatus('online', 'Webhooks Ativos');
            addLog('Webhooks funcionando corretamente', 'success');
        } else {
            throw new Error('Webhooks não respondem');
        }
    } catch (error) {
        updateWebhookStatus('offline', 'Webhooks Inativos');
        addLog(`Erro nos webhooks: ${error.message}`, 'error');
    } finally {
        elements.checkWebhooksBtn.disabled = false;
    }
}

// Iniciar monitoramento de conversas
function startConversationsMonitoring() {
    // Atualizar conversas imediatamente
    refreshConversations();

    // Configurar atualização automática a cada 10 segundos
    if (conversationsRefreshInterval) {
        clearInterval(conversationsRefreshInterval);
    }

    conversationsRefreshInterval = setInterval(refreshConversations, 10000);
    addLog('Monitoramento de conversas iniciado (atualização a cada 10s)', 'success');
}

// Atualizar lista de conversas
async function refreshConversations() {
    if (!conversationsClient) {
        return;
    }

    try {
        elements.refreshConversationsBtn.disabled = true;

        // Buscar conversas via API
        const response = await fetch('/api/conversations');
        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        const conversations = data.conversations || [];

        // Atualizar contador
        elements.conversationsCount.textContent = `${conversations.length} conversa(s) ativa(s)`;

        // Atualizar lista
        updateConversationsList(conversations);

        // Log apenas se houver mudanças
        if (conversations.length !== activeConversations.size) {
            addLog(`📊 ${conversations.length} conversa(s) ativa(s) encontrada(s)`, 'info');
        }

    } catch (error) {
        console.error('Erro ao atualizar conversas:', error);
        addLog(`Erro ao atualizar conversas: ${error.message}`, 'error');
    } finally {
        elements.refreshConversationsBtn.disabled = false;
    }
}

// Atualizar lista de conversas na interface
function updateConversationsList(conversations) {
    const container = elements.conversationsList;

    if (conversations.length === 0) {
        container.innerHTML = `
            <div class="no-conversations">
                <p>📭 Nenhuma conversa ativa</p>
                <small>O sistema está aguardando mensagens dos usuários para criar conversas automaticamente</small>
            </div>
        `;
        return;
    }

    container.innerHTML = '';

    conversations.forEach(conv => {
        const convElement = document.createElement('div');
        convElement.className = 'conversation-item';
        convElement.innerHTML = `
            <div class="conversation-info">
                <h4>${conv.friendlyName || 'Conversa sem nome'}</h4>
                <small>SID: ${conv.sid}</small>
                <small>Criada: ${new Date(conv.dateCreated).toLocaleString()}</small>
            </div>
            <div class="conversation-actions">
                <button class="btn btn-primary btn-small" onclick="openConversation('${conv.sid}', '${conv.friendlyName}')">
                    💬 Abrir Chat
                </button>
                <button class="btn btn-danger btn-small" onclick="closeConversation('${conv.sid}', '${conv.friendlyName}')">
                    🔚 Encerrar
                </button>
            </div>
        `;

        container.appendChild(convElement);

        // Atualizar mapa de conversas ativas
        activeConversations.set(conv.sid, conv);
    });
}



// Abrir conversa para chat (função global)
window.openConversation = async function(conversationSid, friendlyName) {
    if (!conversationsClient) {
        addLog('Conecte-se primeiro como operador', 'error');
        return;
    }

    try {
        addLog(`Abrindo conversa: ${friendlyName}`, 'info');

        // Conectar-se à conversa via SDK
        activeConversation = await conversationsClient.getConversationBySid(conversationSid);

        // Verificar se já está na conversa, se não, juntar-se
        try {
            await activeConversation.join();
            addLog('✅ Conectado à conversa via SDK', 'success');
        } catch (error) {
            // Se já estiver na conversa, isso é normal
            if (error.message.includes('already a participant') || error.message.includes('already joined')) {
                addLog('✅ Já conectado à conversa via SDK', 'success');
            } else if (error.message.includes('Conflict')) {
                addLog(`⚠️ Conflito ao conectar SDK: Operador já está participando desta conversa com outra identidade. Isso é normal quando há múltiplos operadores.`, 'warning');
                addLog(`🔍 Detalhes do conflito: ${error.message}`, 'info');
            } else {
                addLog(`⚠️ Aviso ao conectar SDK: ${error.message}`, 'warning');
                addLog(`🔍 Código do erro: ${error.code || 'N/A'}`, 'info');
            }
        }

        // Configurar eventos da conversa
        setupConversationEvents();

        // Atualizar interface do chat
        updateChatHeader(conversationSid, friendlyName);

        // Limpar mensagens anteriores e carregar mensagens da conversa
        clearMessages();
        await loadConversationMessages();

        // Habilitar controles de chat
        enableChatControls();

        addLog(`Chat ativo: ${friendlyName}`, 'success');

    } catch (error) {
        console.error('Erro ao abrir conversa:', error);
        addLog(`Erro ao abrir conversa: ${error.message}`, 'error');
    }
};

// Encerrar conversa individual (função global)
window.closeConversation = async function(conversationSid, friendlyName) {
    if (!confirm(`Tem certeza que deseja encerrar a conversa "${friendlyName}"?`)) {
        return;
    }

    try {
        addLog(`Encerrando conversa: ${friendlyName}`, 'info');

        const response = await fetch(`/api/conversations/${conversationSid}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog(`✅ ${data.message}`, 'success');

        // Se a conversa ativa foi encerrada, fechar o chat
        if (activeConversation && activeConversation.sid === conversationSid) {
            closeActiveChat();
        }

        // Atualizar lista de conversas
        refreshConversations();

    } catch (error) {
        console.error('Erro ao encerrar conversa:', error);
        addLog(`Erro ao encerrar conversa: ${error.message}`, 'error');
    }
};

// Encerrar todas as conversas
async function closeAllConversations() {
    if (!confirm('Tem certeza que deseja encerrar TODAS as conversas ativas? Esta ação não pode ser desfeita.')) {
        return;
    }

    try {
        elements.closeAllConversationsBtn.disabled = true;
        elements.closeAllConversationsBtn.textContent = '⏳ Encerrando...';

        addLog('Encerrando todas as conversas ativas...', 'info');

        const response = await fetch('/api/conversations/close-all/execute', {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        addLog(`✅ ${data.message}`, 'success');

        if (data.errorCount > 0) {
            addLog(`⚠️ ${data.errorCount} erro(s) encontrado(s)`, 'warning');
        }

        // Fechar chat ativo se houver
        if (activeConversation) {
            closeActiveChat();
        }

        // Atualizar lista de conversas
        refreshConversations();

    } catch (error) {
        console.error('Erro ao encerrar todas as conversas:', error);
        addLog(`Erro ao encerrar todas as conversas: ${error.message}`, 'error');
    } finally {
        elements.closeAllConversationsBtn.disabled = false;
        elements.closeAllConversationsBtn.textContent = '🔚 Encerrar Todas';
    }
}

// Fechar chat ativo
function closeActiveChat() {
    if (activeConversation) {
        addLog(`Fechando chat: ${activeConversation.friendlyName}`, 'info');

        // Limpar eventos da conversa
        if (activeConversation.removeAllListeners) {
            activeConversation.removeAllListeners();
        }

        activeConversation = null;
    }

    // Atualizar interface
    elements.chatHeader.style.display = 'none';
    elements.activeChatTitle.textContent = 'Selecione uma conversa';
    elements.activeChatDetails.textContent = 'Nenhuma conversa selecionada';

    // Desabilitar controles de chat
    disableChatControls();

    // Limpar mensagens
    clearMessages();
    addSystemMessage('👋 Selecione uma conversa da lista para iniciar o chat');

    addLog('Chat fechado', 'info');
}

// Carregar mensagens da conversa
async function loadConversationMessages() {
    if (!activeConversation) {
        return;
    }

    try {
        const messages = await activeConversation.getMessages();

        messages.items.forEach(message => {
            const isOwnMessage = message.author === currentUserIdentity;
            addMessage(message.body, isOwnMessage ? 'sent' : 'received', message.author, message.dateCreated);
        });

        addLog(`${messages.items.length} mensagem(s) carregada(s)`, 'info');

    } catch (error) {
        console.error('Erro ao carregar mensagens:', error);
        addLog(`Erro ao carregar mensagens: ${error.message}`, 'error');
    }
}

// Enviar mensagem
async function handleSendMessage() {
    if (!activeConversation) {
        addLog('Nenhuma conversa ativa', 'error');
        return;
    }

    const messageText = elements.messageInput.value.trim();

    if (!messageText) {
        return;
    }

    try {
        // Usar nossa API em vez do SDK direto para suportar WhatsApp híbrido
        const response = await fetch(`/api/conversations/${activeConversation.sid}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                body: messageText,
                author: 'operador-1'
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Erro ao enviar mensagem');
        }

        const result = await response.json();

        elements.messageInput.value = '';

        // Log detalhado baseado no método usado
        if (result.method === 'whatsapp_direct') {
            addLog(`✅ Mensagem WhatsApp enviada: ${result.messageSid}`, 'success');
        } else {
            addLog(`✅ Mensagem enviada via Conversations: ${result.messageSid}`, 'success');
        }

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        addLog(`❌ Erro ao enviar mensagem: ${error.message}`, 'error');
    }
}

// Manipular tecla Enter no input de mensagem
function handleMessageKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
    }
}

// Manipular typing indicator
function handleTyping() {
    if (!activeConversation) {
        return;
    }

    // Verificar se é conversa WhatsApp
    const isWhatsAppConversation = activeConversation.friendlyName &&
                                  activeConversation.friendlyName.includes('WhatsApp');

    if (isWhatsAppConversation) {
        // Para WhatsApp, apenas mostrar localmente (não enviar via API)
        addLog('⌨️ Digitando... (WhatsApp não suporta typing indicator)', 'warning');
        return;
    }

    // Para outras conversas, enviar typing indicator normal
    try {
        activeConversation.typing();
        addLog('Typing indicator enviado', 'success');
    } catch (error) {
        console.error('Erro ao enviar typing indicator:', error);
        addLog(`Erro ao enviar typing indicator: ${error.message}`, 'error');
    }
}

// Configurar eventos da conversa
function setupConversationEvents() {
    if (!activeConversation) return;

    // Mensagens
    activeConversation.on('messageAdded', handleMessageAdded);
    
    // Typing indicators
    activeConversation.on('typingStarted', handleTypingStarted);
    activeConversation.on('typingEnded', handleTypingEnded);
    
    // Participantes
    activeConversation.on('participantJoined', handleParticipantJoined);
    activeConversation.on('participantLeft', handleParticipantLeft);

    addLog('Event listeners da conversa configurados', 'success');
}

// Manipular nova mensagem
function handleMessageAdded(message) {
    addLog(`Nova mensagem de ${message.author || 'Sistema'}: ${message.body}`, 'success');
    
    const isOwnMessage = message.author === currentUserIdentity;
    addMessage(message.body, isOwnMessage ? 'sent' : 'received', message.author, message.dateCreated);
}

// Manipular início de digitação
function handleTypingStarted(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} começou a digitar`, 'warning');
        showTypingIndicator(participant.identity || 'Alguém');
    }
}

// Manipular fim de digitação
function handleTypingEnded(participant) {
    if (participant.identity !== currentUserIdentity) {
        addLog(`${participant.identity || 'Alguém'} parou de digitar`, 'warning');
        hideTypingIndicator();
    }
}

// Manipular participante entrando
function handleParticipantJoined(participant) {
    addLog(`Participante entrou: ${participant.identity || participant.address}`, 'success');
    addSystemMessage(`${participant.identity || participant.address} entrou na conversa`);
}

// Manipular participante saindo
function handleParticipantLeft(participant) {
    addLog(`Participante saiu: ${participant.identity || participant.address}`, 'warning');
    addSystemMessage(`${participant.identity || participant.address} saiu da conversa`);
}

// Manipular mudança de estado de conexão
function handleConnectionStateChanged(state) {
    addLog(`Estado de conexão: ${state}`, 'warning');
    
    switch (state) {
        case 'connected':
            updateConnectionStatus('online', 'Conectado');
            break;
        case 'connecting':
            updateConnectionStatus('connecting', 'Conectando...');
            break;
        case 'disconnected':
            updateConnectionStatus('offline', 'Desconectado');
            break;
        case 'denied':
            updateConnectionStatus('offline', 'Conexão negada');
            break;
    }
}

// Manipular conversa juntada
function handleConversationJoined(conversation) {
    addLog(`Juntou-se à conversa: ${conversation.friendlyName}`, 'success');
}

// Manipular conversa deixada
function handleConversationLeft(conversation) {
    addLog(`Deixou a conversa: ${conversation.friendlyName}`, 'warning');
}

// Utilitários de UI
function updateConnectionStatus(status, text) {
    const indicator = elements.connectionStatus.querySelector('.status-indicator');
    const statusText = elements.connectionStatus.querySelector('.status-text');

    indicator.className = `status-indicator ${status}`;
    statusText.textContent = text;
}

function updateWebhookStatus(status, text) {
    const indicator = elements.webhookStatus.querySelector('.status-indicator');
    const statusText = elements.webhookStatus.querySelector('.status-text');

    indicator.className = `status-indicator ${status}`;
    statusText.textContent = text;
}

function updateChatHeader(conversationSid, friendlyName) {
    elements.activeChatTitle.textContent = friendlyName || 'Conversa sem nome';
    elements.activeChatDetails.textContent = `SID: ${conversationSid}`;
    elements.chatHeader.style.display = 'flex';
}

function enableChatControls() {
    elements.messageInput.disabled = false;
    elements.sendBtn.disabled = false;
    elements.messageInput.placeholder = 'Digite sua resposta...';
    elements.messageInput.focus();
}

function disableChatControls() {
    elements.messageInput.disabled = true;
    elements.sendBtn.disabled = true;
    elements.messageInput.placeholder = 'Selecione uma conversa para responder...';
    elements.messageInput.value = '';
}

function clearMessages() {
    elements.messagesContainer.innerHTML = '';
}

function addMessage(text, type, author, timestamp) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    const messageContent = document.createElement('p');
    messageContent.textContent = text;
    messageDiv.appendChild(messageContent);
    
    if (author && timestamp) {
        const metaDiv = document.createElement('div');
        metaDiv.className = 'message-meta';
        metaDiv.textContent = `${author} - ${new Date(timestamp).toLocaleTimeString()}`;
        messageDiv.appendChild(metaDiv);
    }
    
    elements.messagesContainer.appendChild(messageDiv);
    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
}

function addSystemMessage(text) {
    addMessage(text, 'system');
}

function showTypingIndicator(user) {
    const typingText = elements.typingIndicator.querySelector('.typing-text');
    typingText.textContent = `${user} está digitando...`;
    elements.typingIndicator.style.display = 'flex';
}

function hideTypingIndicator() {
    elements.typingIndicator.style.display = 'none';
}

function addLog(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    const timestamp = document.createElement('span');
    timestamp.className = 'timestamp';
    timestamp.textContent = `[${new Date().toLocaleTimeString()}]`;
    
    const logMessage = document.createElement('span');
    logMessage.className = 'log-message';
    logMessage.textContent = message;
    
    logEntry.appendChild(timestamp);
    logEntry.appendChild(logMessage);
    
    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}

function clearLogs() {
    elements.logsContainer.innerHTML = '';
    addLog('Logs limpos', 'success');
}

// Buscar logs do servidor
async function refreshServerLogs() {
    try {
        elements.refreshLogsBtn.disabled = true;
        elements.refreshLogsBtn.textContent = 'Carregando...';

        const response = await fetch('/webhooks/logs?hours=1');
        if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
        }

        const data = await response.json();

        if (data.logs && data.logs.length > 0) {
            addLog(`📊 Carregados ${data.logs.length} logs do servidor`, 'success');

            // Adicionar logs do servidor
            data.logs.forEach(log => {
                const logType = getLogTypeFromServerLog(log);
                const message = formatServerLogMessage(log);
                addServerLog(message, logType, log.timestamp);
            });
        } else {
            addLog('📊 Nenhum log encontrado no servidor', 'warning');
        }

    } catch (error) {
        console.error('Erro ao buscar logs do servidor:', error);
        addLog(`❌ Erro ao buscar logs: ${error.message}`, 'error');
    } finally {
        elements.refreshLogsBtn.disabled = false;
        elements.refreshLogsBtn.textContent = 'Atualizar Logs';
    }
}

// Alternar auto-refresh de logs
function toggleAutoRefresh() {
    autoRefreshLogs = !autoRefreshLogs;

    if (autoRefreshLogs) {
        elements.toggleAutoRefreshBtn.textContent = 'Auto-refresh: ON';
        elements.toggleAutoRefreshBtn.classList.add('btn-primary');
        elements.toggleAutoRefreshBtn.classList.remove('btn-secondary');

        // Atualizar logs a cada 10 segundos
        autoRefreshInterval = setInterval(refreshServerLogs, 10000);
        addLog('🔄 Auto-refresh de logs ativado (10s)', 'success');
    } else {
        elements.toggleAutoRefreshBtn.textContent = 'Auto-refresh: OFF';
        elements.toggleAutoRefreshBtn.classList.remove('btn-primary');
        elements.toggleAutoRefreshBtn.classList.add('btn-secondary');

        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
        addLog('🔄 Auto-refresh de logs desativado', 'warning');
    }
}

// Determinar tipo de log baseado no log do servidor
function getLogTypeFromServerLog(log) {
    if (log.type.includes('error')) return 'error';
    if (log.type.includes('webhook')) return 'success';
    if (log.type.includes('message')) return 'success';
    if (log.type.includes('typing')) return 'warning';
    return 'info';
}

// Formatar mensagem do log do servidor
function formatServerLogMessage(log) {
    const timestamp = new Date(log.timestamp).toLocaleTimeString();

    switch (log.type) {
        case 'webhook_received':
            return `🌐 Webhook recebido: ${log.url} - ${log.body?.Body || log.body?.EventType || 'N/A'}`;
        case 'message_received':
            return `📱 Mensagem recebida: "${log.body}" de ${log.from}`;
        case 'message_sent':
            return `📤 Mensagem enviada: "${log.body}" para ${log.to}`;
        case 'typing_indicator':
            return `⌨️ Typing ${log.action}: ${log.participant}`;
        case 'conversation_event':
            return `💬 Evento: ${log.eventType} - ${log.body || 'N/A'}`;
        case 'error':
            return `❌ Erro: ${log.message}`;
        default:
            return `📋 ${log.type}: ${JSON.stringify(log).substring(0, 100)}...`;
    }
}

// Adicionar log do servidor com timestamp específico
function addServerLog(message, type = 'info', timestamp) {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry server-log ${type}`;

    const timestampSpan = document.createElement('span');
    timestampSpan.className = 'timestamp';
    timestampSpan.textContent = `[${new Date(timestamp).toLocaleTimeString()}]`;

    const logMessage = document.createElement('span');
    logMessage.className = 'log-message';
    logMessage.textContent = message;

    logEntry.appendChild(timestampSpan);
    logEntry.appendChild(logMessage);

    elements.logsContainer.appendChild(logEntry);
    elements.logsContainer.scrollTop = elements.logsContainer.scrollHeight;
}
