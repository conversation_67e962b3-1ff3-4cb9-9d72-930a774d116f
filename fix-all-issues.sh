#!/bin/bash

# Script para corrigir todos os problemas identificados
# Testes unitários completos antes de declarar sucesso

echo "🔧 CORREÇÃO COMPLETA - Todos os Problemas"
echo "========================================="

cd /Users/<USER>/Documents/augment-projects/twiliowhats

echo ""
echo "📋 1. Reiniciando Sistema com Correções"
echo "======================================"

# Parar servidor atual
echo "🛑 Parando servidor atual..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
sleep 2

# Iniciar servidor com correções
echo "🚀 Iniciando servidor com correções..."
npm start > server.log 2>&1 &
sleep 5

if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Falha ao iniciar servidor"
    echo "📋 Logs do servidor:"
    tail -10 server.log
    exit 1
fi

echo "✅ Servidor reiniciado com correções"

echo ""
echo "📋 2. Configurando ngrok para Webhooks Reais"
echo "==========================================="

# Verificar se ngrok está rodando
NGROK_STATUS=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null)
if echo "$NGROK_STATUS" | grep -q "https://"; then
    NGROK_URL=$(echo "$NGROK_STATUS" | jq -r '.tunnels[0].public_url' 2>/dev/null)
    echo "✅ ngrok já está rodando: $NGROK_URL"
else
    echo "🚀 Iniciando ngrok..."
    ./setup-ngrok.sh &
    NGROK_SETUP_PID=$!
    
    # Aguardar ngrok inicializar
    sleep 10
    
    # Verificar se ngrok iniciou
    NGROK_STATUS=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null)
    if echo "$NGROK_STATUS" | grep -q "https://"; then
        NGROK_URL=$(echo "$NGROK_STATUS" | jq -r '.tunnels[0].public_url' 2>/dev/null)
        echo "✅ ngrok iniciado: $NGROK_URL"
        
        # Parar o script de setup
        kill $NGROK_SETUP_PID 2>/dev/null || true
    else
        echo "❌ Falha ao iniciar ngrok"
        echo "⚠️  Continuando com testes locais..."
        NGROK_URL=""
    fi
fi

echo ""
echo "📋 3. Limpando Estado Anterior"
echo "============================="

# Encerrar todas as conversas
echo "🔚 Encerrando todas as conversas..."
CLEANUP=$(curl -s -X DELETE "http://localhost:3000/api/conversations/close-all/execute")
CLOSED_COUNT=$(echo "$CLEANUP" | jq -r '.successCount' 2>/dev/null || echo "0")
echo "✅ $CLOSED_COUNT conversas encerradas"

echo ""
echo "📋 4. TESTE UNITÁRIO 1 - Criação de Conversa"
echo "==========================================="

echo "🧪 Testando criação de conversa com formatação corrigida..."

CREATE_TEST=$(curl -s -X POST "http://localhost:3000/api/conversations/connect-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "whatsappNumber": "+5551993590210",
    "userIdentity": "teste-unitario-1",
    "friendlyName": "Teste Unitário 1 - Formatação Corrigida"
  }')

echo "📤 Resultado da criação:"
echo "$CREATE_TEST" | jq '.' 2>/dev/null || echo "$CREATE_TEST"

if echo "$CREATE_TEST" | grep -q '"success":true'; then
    CONV_SID_1=$(echo "$CREATE_TEST" | jq -r '.conversation.conversationSid' 2>/dev/null)
    PROXY_ADDR_1=$(echo "$CREATE_TEST" | jq -r '.participants.whatsapp.data.proxyAddress' 2>/dev/null)
    
    echo "✅ TESTE 1 PASSOU: Conversa criada"
    echo "   SID: $CONV_SID_1"
    echo "   Proxy: $PROXY_ADDR_1"
    
    if [ "$PROXY_ADDR_1" != "null" ] && [ "$PROXY_ADDR_1" != "" ]; then
        echo "✅ TESTE 1.1 PASSOU: Proxy Address definido"
        
        # Testar envio de mensagem
        echo ""
        echo "💬 TESTE 1.2 - Envio de mensagem..."
        
        SEND_TEST_1=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONV_SID_1/messages" \
          -H "Content-Type: application/json" \
          -d '{
            "body": "🧪 TESTE UNITÁRIO 1 - Mensagem deve funcionar!",
            "author": "teste-unitario-1"
          }')
        
        if echo "$SEND_TEST_1" | grep -q '"messageSid"'; then
            MSG_SID_1=$(echo "$SEND_TEST_1" | jq -r '.messageSid' 2>/dev/null)
            echo "✅ TESTE 1.2 PASSOU: Mensagem enviada ($MSG_SID_1)"
        else
            echo "❌ TESTE 1.2 FALHOU: Falha ao enviar mensagem"
            echo "$SEND_TEST_1"
        fi
        
    else
        echo "❌ TESTE 1.1 FALHOU: Proxy Address não definido"
    fi
    
else
    echo "❌ TESTE 1 FALHOU: Falha ao criar conversa"
    echo "📋 Erro: $(echo "$CREATE_TEST" | jq -r '.error' 2>/dev/null || echo "$CREATE_TEST")"
fi

echo ""
echo "📋 5. TESTE UNITÁRIO 2 - Webhook com Formatação Corrigida"
echo "========================================================"

echo "🧪 Testando webhook com número formatado corretamente..."

WEBHOOK_TEST_2=$(curl -s -X POST "http://localhost:3000/webhooks/whatsapp/incoming" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+*************&To=whatsapp:+***********&Body=Teste%20unitario%202%20webhook&MessageSid=UNIT_TEST_2_$(date +%s)&AccountSid=AC04266eaa4a7caf821e8dac9d92879e95&ApiVersion=2010-04-01")

echo "📤 Resposta do webhook: $WEBHOOK_TEST_2"

sleep 5

# Verificar se criou conversa
CONV_AFTER_2=$(curl -s http://localhost:3000/api/conversations)
CONV_COUNT_2=$(echo "$CONV_AFTER_2" | jq '.conversations | length' 2>/dev/null || echo "0")

echo "📋 Conversas após webhook: $CONV_COUNT_2"

if [ "$CONV_COUNT_2" -gt 1 ]; then
    echo "✅ TESTE 2 PASSOU: Webhook criou conversa automática"
    
    # Encontrar a nova conversa
    NEW_CONV=$(echo "$CONV_AFTER_2" | jq '.conversations[] | select(.friendlyName | contains("*************"))' 2>/dev/null)
    if [ ! -z "$NEW_CONV" ]; then
        NEW_CONV_SID=$(echo "$NEW_CONV" | jq -r '.sid' 2>/dev/null)
        echo "✅ TESTE 2.1 PASSOU: Nova conversa encontrada ($NEW_CONV_SID)"
    else
        echo "❌ TESTE 2.1 FALHOU: Nova conversa não encontrada"
    fi
    
else
    echo "❌ TESTE 2 FALHOU: Webhook não criou conversa automática"
    
    # Verificar logs de erro
    echo "🔍 Verificando logs de erro..."
    tail -5 logs/twilio-$(date +%Y-%m-%d).log | grep -E "(error|Error)" || echo "Nenhum erro recente encontrado"
fi

echo ""
echo "📋 6. TESTE UNITÁRIO 3 - Mensagem Direta"
echo "======================================="

echo "🧪 Testando sistema de mensagem direta..."

DIRECT_TEST_3=$(curl -s -X POST "http://localhost:3000/api/conversations/send-direct-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "+5551993590210",
    "body": "🧪 TESTE UNITÁRIO 3 - Mensagem direta funcionando!"
  }')

echo "📤 Resultado da mensagem direta:"
echo "$DIRECT_TEST_3" | jq '.' 2>/dev/null || echo "$DIRECT_TEST_3"

if echo "$DIRECT_TEST_3" | grep -q '"success":true'; then
    MSG_SID_3=$(echo "$DIRECT_TEST_3" | jq -r '.messageSid' 2>/dev/null)
    SENDER_3=$(echo "$DIRECT_TEST_3" | jq -r '.sender' 2>/dev/null)
    echo "✅ TESTE 3 PASSOU: Mensagem direta enviada"
    echo "   Message SID: $MSG_SID_3"
    echo "   Sender: $SENDER_3"
else
    echo "❌ TESTE 3 FALHOU: Falha na mensagem direta"
fi

echo ""
echo "📋 7. TESTE UNITÁRIO 4 - Encerramento de Conversas"
echo "================================================="

echo "🧪 Testando encerramento individual..."

if [ ! -z "$CONV_SID_1" ]; then
    CLOSE_TEST_4=$(curl -s -X DELETE "http://localhost:3000/api/conversations/$CONV_SID_1")
    
    if echo "$CLOSE_TEST_4" | grep -q '"message"'; then
        echo "✅ TESTE 4 PASSOU: Encerramento individual funcionando"
    else
        echo "❌ TESTE 4 FALHOU: Falha no encerramento individual"
        echo "$CLOSE_TEST_4"
    fi
else
    echo "⚠️  TESTE 4 PULADO: Nenhuma conversa para encerrar"
fi

echo ""
echo "📋 8. TESTE UNITÁRIO 5 - Interface Web"
echo "====================================="

echo "🧪 Testando endpoints da interface..."

# Teste autenticação
AUTH_TEST_5=$(curl -s http://localhost:3000/api/auth/verify)
if echo "$AUTH_TEST_5" | grep -q '"status":"OK"'; then
    echo "✅ TESTE 5.1 PASSOU: Autenticação"
else
    echo "❌ TESTE 5.1 FALHOU: Autenticação"
fi

# Teste listagem
LIST_TEST_5=$(curl -s http://localhost:3000/api/conversations)
if echo "$LIST_TEST_5" | grep -q '"conversations"'; then
    echo "✅ TESTE 5.2 PASSOU: Listagem de conversas"
else
    echo "❌ TESTE 5.2 FALHOU: Listagem de conversas"
fi

# Teste webhook test
WEBHOOK_TEST_5=$(curl -s http://localhost:3000/webhooks/test)
if echo "$WEBHOOK_TEST_5" | grep -q "funcionando"; then
    echo "✅ TESTE 5.3 PASSOU: Webhooks básicos"
else
    echo "❌ TESTE 5.3 FALHOU: Webhooks básicos"
fi

echo ""
echo "📋 RESUMO DOS TESTES UNITÁRIOS"
echo "============================="

# Contar testes que passaram
PASSED_TESTS=0
TOTAL_TESTS=8

# Verificar cada teste (simplificado)
if echo "$CREATE_TEST" | grep -q '"success":true'; then ((PASSED_TESTS++)); fi
if [ "$PROXY_ADDR_1" != "null" ] && [ "$PROXY_ADDR_1" != "" ]; then ((PASSED_TESTS++)); fi
if echo "$SEND_TEST_1" | grep -q '"messageSid"'; then ((PASSED_TESTS++)); fi
if [ "$CONV_COUNT_2" -gt 1 ]; then ((PASSED_TESTS++)); fi
if echo "$DIRECT_TEST_3" | grep -q '"success":true'; then ((PASSED_TESTS++)); fi
if echo "$CLOSE_TEST_4" | grep -q '"message"'; then ((PASSED_TESTS++)); fi
if echo "$AUTH_TEST_5" | grep -q '"status":"OK"'; then ((PASSED_TESTS++)); fi
if echo "$LIST_TEST_5" | grep -q '"conversations"'; then ((PASSED_TESTS++)); fi

echo "📊 Resultado: $PASSED_TESTS/$TOTAL_TESTS testes passaram"

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    echo ""
    echo "🎉 TODOS OS TESTES PASSARAM!"
    echo "✅ Sistema 100% funcional"
    echo ""
    echo "📱 TESTE MANUAL FINAL:"
    echo "====================="
    if [ ! -z "$NGROK_URL" ]; then
        echo "1. 🌐 Configure webhooks na Twilio Console:"
        echo "   WhatsApp: $NGROK_URL/webhooks/whatsapp/incoming"
        echo "2. 📱 Envie mensagem WhatsApp para: +***********"
        echo "3. 🌐 Acesse: http://localhost:3000"
        echo "4. 👀 Veja a conversa aparecer automaticamente"
        echo "5. 💬 Responda através da interface"
    else
        echo "1. 🔧 Configure ngrok primeiro: ./setup-ngrok.sh"
        echo "2. 📱 Configure webhooks na Twilio Console"
        echo "3. 🧪 Teste com mensagem real do WhatsApp"
    fi
    
elif [ "$PASSED_TESTS" -ge 6 ]; then
    echo ""
    echo "⚠️  MAIORIA DOS TESTES PASSOU ($PASSED_TESTS/$TOTAL_TESTS)"
    echo "✅ Sistema parcialmente funcional"
    echo "🔧 Alguns ajustes podem ser necessários"
    
else
    echo ""
    echo "❌ MUITOS TESTES FALHARAM ($PASSED_TESTS/$TOTAL_TESTS)"
    echo "🔧 Correções adicionais necessárias"
    echo ""
    echo "🔍 Verificar logs para mais detalhes:"
    echo "   tail -f logs/twilio-$(date +%Y-%m-%d).log"
fi

echo ""
echo "🔍 Para monitorar logs em tempo real:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"

echo ""
echo "🌐 Interface disponível em: http://localhost:3000"
