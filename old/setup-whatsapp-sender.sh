#!/bin/bash

# Script para configurar WhatsApp Sender registrado
# Twilio WhatsApp Typing Indicator Test

echo "📱 Configurando WhatsApp Sender Registrado"
echo "=========================================="

# Carregar variáveis do .env
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Arquivo .env não encontrado!"
    exit 1
fi

# Verificar se o WhatsApp Sender está configurado
if [ -z "$TWILIO_WHATSAPP_SENDER_NUMBER" ] || [ "$TWILIO_WHATSAPP_SENDER_NUMBER" = "whatsapp:+your_registered_number_here" ]; then
    echo "❌ TWILIO_WHATSAPP_SENDER_NUMBER não configurado no arquivo .env"
    echo "📝 Configure TWILIO_WHATSAPP_SENDER_NUMBER=whatsapp:+18382700077 no arquivo .env"
    exit 1
fi

echo "✅ WhatsApp Sender configurado: $TWILIO_WHATSAPP_SENDER_NUMBER"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando na porta 3000"
    echo "🚀 Inicie o servidor primeiro com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando na porta 3000"

# Verificar se há túnel ativo (ngrok ou localtunnel)
TUNNEL_URL=""

# Verificar ngrok
if curl -s http://localhost:4040/api/tunnels > /dev/null 2>&1; then
    TUNNEL_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null)
    if [ "$TUNNEL_URL" != "null" ] && [ ! -z "$TUNNEL_URL" ]; then
        echo "✅ Túnel ngrok detectado: $TUNNEL_URL"
    fi
fi

# Verificar localtunnel se ngrok não estiver ativo
if [ -z "$TUNNEL_URL" ] || [ "$TUNNEL_URL" = "null" ]; then
    if [ -f "localtunnel.pid" ]; then
        LT_PID=$(cat localtunnel.pid)
        if kill -0 $LT_PID 2>/dev/null; then
            # Tentar extrair URL do log
            if [ -f "localtunnel.log" ]; then
                TUNNEL_URL=$(grep -o 'https://[^[:space:]]*\.loca\.lt' localtunnel.log | tail -1)
                if [ ! -z "$TUNNEL_URL" ]; then
                    echo "✅ Túnel LocalTunnel detectado: $TUNNEL_URL"
                fi
            fi
        fi
    fi
fi

if [ -z "$TUNNEL_URL" ] || [ "$TUNNEL_URL" = "null" ]; then
    echo "❌ Nenhum túnel público detectado!"
    echo "🚀 Execute primeiro:"
    echo "   ./setup-ngrok-programmatic.sh"
    echo "   OU"
    echo "   ./setup-localtunnel.sh"
    exit 1
fi

# Gerar URLs dos webhooks para WhatsApp Sender
WEBHOOK_INCOMING="$TUNNEL_URL/webhooks/whatsapp/incoming"
WEBHOOK_STATUS="$TUNNEL_URL/webhooks/whatsapp/status"
WEBHOOK_CONVERSATIONS="$TUNNEL_URL/webhooks/conversations"

echo ""
echo "📋 URLs dos Webhooks para WhatsApp Sender"
echo "========================================="
echo ""
echo "📱 WhatsApp Sender: $TWILIO_WHATSAPP_SENDER_NUMBER"
echo "🌐 Túnel público: $TUNNEL_URL"
echo ""
echo "🔗 URLs para configurar na Twilio:"
echo ""
echo "1️⃣ WhatsApp Sender - Webhook URL:"
echo "   $WEBHOOK_INCOMING"
echo ""
echo "2️⃣ WhatsApp Sender - Status callback URL:"
echo "   $WEBHOOK_STATUS"
echo ""
echo "3️⃣ Conversations Service - Webhook URL:"
echo "   $WEBHOOK_CONVERSATIONS"
echo ""

# Testar webhooks
echo "🧪 Testando endpoints dos webhooks..."
echo ""

# Teste webhook test
TEST_RESULT=$(curl -s "$TUNNEL_URL/webhooks/test" 2>/dev/null)
if [[ "$TEST_RESULT" =~ "Webhooks funcionando" ]]; then
    echo "✅ Webhook de teste: OK"
else
    echo "❌ Webhook de teste: FALHOU"
    echo "   Resposta: $TEST_RESULT"
fi

# Salvar URLs em arquivo para referência
cat > webhook-urls-sender.txt << EOF
# URLs dos Webhooks para WhatsApp Sender - Gerado em $(date)
# WhatsApp Sender: $TWILIO_WHATSAPP_SENDER_NUMBER
# Configure estas URLs no Twilio Console

## WhatsApp Sender Configuration
Webhook URL: $WEBHOOK_INCOMING
Status callback URL: $WEBHOOK_STATUS

## Conversations Service  
Webhook URL: $WEBHOOK_CONVERSATIONS

## URLs para teste
Público: $TUNNEL_URL
Teste: $TUNNEL_URL/webhooks/test
Logs: $TUNNEL_URL/webhooks/logs

## Como configurar no Twilio Console:

### 1. Configurar WhatsApp Sender:
   - Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
   - Encontre o sender: +18382700077
   - Configure:
     * Webhook URL: $WEBHOOK_INCOMING
     * Status callback URL: $WEBHOOK_STATUS
     * HTTP Method: POST

### 2. Configurar Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione seu service: $TWILIO_CONVERSATIONS_SERVICE_SID
   - Configure:
     * Webhook URL: $WEBHOOK_CONVERSATIONS
     * HTTP Method: POST
     * Events: onMessageAdded, onTypingStarted, onTypingEnded

## Comandos úteis:
- Ver logs: curl $TUNNEL_URL/webhooks/logs
- Testar webhook: curl $TUNNEL_URL/webhooks/test
- Limpar participantes: curl -X DELETE $TUNNEL_URL/api/conversations/cleanup-whatsapp/+5551993590210

## Diferenças do Sandbox:
- ✅ Não precisa de "join" keyword
- ✅ Pode enviar para qualquer número
- ✅ Sem limitações de sandbox
- ✅ Mais estável e confiável
EOF

echo "📄 URLs salvas em: webhook-urls-sender.txt"
echo ""
echo "🔧 Próximos passos:"
echo "=================="
echo ""
echo "1️⃣ Configure WhatsApp Sender:"
echo "   - Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp"
echo "   - Encontre: +18382700077"
echo "   - Configure Webhook URL: $WEBHOOK_INCOMING"
echo "   - Configure Status callback URL: $WEBHOOK_STATUS"
echo ""
echo "2️⃣ Configure Conversations Service:"
echo "   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services"
echo "   - Configure Webhook URL: $WEBHOOK_CONVERSATIONS"
echo "   - Marque eventos: onMessageAdded, onTypingStarted, onTypingEnded"
echo ""
echo "3️⃣ Teste a aplicação:"
echo "   - Conecte na aplicação web"
echo "   - Conecte ao WhatsApp"
echo "   - Envie mensagens em ambas direções"
echo "   - Teste typing indicator"
echo ""
echo "💡 Vantagens do WhatsApp Sender:"
echo "   ✅ Sem limitações de sandbox"
echo "   ✅ Pode enviar para qualquer número"
echo "   ✅ Não precisa de 'join' keyword"
echo "   ✅ Mais estável e confiável"
echo ""
echo "🎯 Agora configure os webhooks na Twilio e teste!"
