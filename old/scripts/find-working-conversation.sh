#!/bin/bash

# Script para encontrar a conversa onde as mensagens funcionaram
# Busca por participantes WhatsApp com proxy definido

echo "🔍 BUSCA - Conversa onde as Mensagens Funcionaram"
echo "================================================="

echo "📱 Mensagens recebidas:"
echo "   1. 'Teste de entrega - sistema corrigido'"
echo "   2. '🧪 TESTE COM API PADRÃO - Esta mensagem deve chegar no WhatsApp!'"
echo ""
echo "🎯 Objetivo: Encontrar qual conversa tinha participante WhatsApp com proxy correto"

echo ""
echo "🔍 Analisando todas as conversas..."

# Criar script Node.js para analisar todas as conversas
cat > find_working_conversation.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function findWorkingConversation() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        console.log('🔍 Buscando todas as conversas...');
        
        // Obter todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        console.log(`📋 Total de conversas encontradas: ${conversations.length}`);
        
        let conversationsWithWhatsApp = [];
        
        // Analisar cada conversa
        for (const conversation of conversations) {
            try {
                console.log(`\n🔍 Analisando conversa: ${conversation.sid}`);
                console.log(`   Nome: ${conversation.friendlyName}`);
                console.log(`   Estado: ${conversation.state}`);
                
                // Obter participantes
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Procurar participantes WhatsApp
                const whatsappParticipants = participants.filter(p =>
                    p.messagingBinding && 
                    p.messagingBinding.address && 
                    p.messagingBinding.address.includes('whatsapp:')
                );

                if (whatsappParticipants.length > 0) {
                    console.log(`   📱 ${whatsappParticipants.length} participante(s) WhatsApp encontrado(s)`);
                    
                    whatsappParticipants.forEach((p, index) => {
                        console.log(`      ${index + 1}. SID: ${p.sid}`);
                        console.log(`         Address: ${p.messagingBinding.address}`);
                        console.log(`         Proxy: ${p.messagingBinding.proxyAddress || 'UNDEFINED'}`);
                        console.log(`         Criado: ${p.dateCreated}`);
                        
                        if (p.messagingBinding.proxyAddress) {
                            console.log(`         ✅ ESTE PARTICIPANTE TEM PROXY!`);
                        }
                    });
                    
                    conversationsWithWhatsApp.push({
                        conversation: conversation,
                        whatsappParticipants: whatsappParticipants
                    });
                } else {
                    console.log(`   📭 Nenhum participante WhatsApp`);
                }
                
            } catch (error) {
                console.log(`   ❌ Erro ao analisar conversa: ${error.message}`);
            }
        }
        
        console.log(`\n📊 RESUMO DA BUSCA:`);
        console.log(`   Total de conversas: ${conversations.length}`);
        console.log(`   Conversas com WhatsApp: ${conversationsWithWhatsApp.length}`);
        
        // Analisar conversas com WhatsApp
        let conversationsWithProxy = [];
        
        conversationsWithWhatsApp.forEach(item => {
            const withProxy = item.whatsappParticipants.filter(p => p.messagingBinding.proxyAddress);
            if (withProxy.length > 0) {
                conversationsWithProxy.push({
                    conversation: item.conversation,
                    participantsWithProxy: withProxy
                });
            }
        });
        
        console.log(`   Conversas com proxy definido: ${conversationsWithProxy.length}`);
        
        if (conversationsWithProxy.length > 0) {
            console.log(`\n🎉 CONVERSAS COM PROXY ENCONTRADAS:`);
            
            conversationsWithProxy.forEach((item, index) => {
                console.log(`\n🎯 Conversa ${index + 1}:`);
                console.log(`   SID: ${item.conversation.sid}`);
                console.log(`   Nome: ${item.conversation.friendlyName}`);
                console.log(`   Estado: ${item.conversation.state}`);
                console.log(`   Participantes com proxy: ${item.participantsWithProxy.length}`);
                
                item.participantsWithProxy.forEach((p, pIndex) => {
                    console.log(`\n   📱 Participante ${pIndex + 1}:`);
                    console.log(`      SID: ${p.sid}`);
                    console.log(`      Address: ${p.messagingBinding.address}`);
                    console.log(`      Proxy: ${p.messagingBinding.proxyAddress}`);
                    console.log(`      Criado: ${p.dateCreated}`);
                });
                
                console.log(`\n   💡 ESTA CONVERSA PODE TER SIDO ONDE AS MENSAGENS FUNCIONARAM!`);
            });
            
            // Verificar mensagens nas conversas com proxy
            console.log(`\n🔍 Verificando mensagens nas conversas com proxy...`);
            
            for (const item of conversationsWithProxy) {
                try {
                    console.log(`\n📝 Mensagens da conversa ${item.conversation.sid}:`);
                    
                    let messages;
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        messages = await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(item.conversation.sid)
                            .messages
                            .list({ limit: 20 });
                    } else {
                        messages = await client.conversations.v1
                            .conversations(item.conversation.sid)
                            .messages
                            .list({ limit: 20 });
                    }
                    
                    const relevantMessages = messages.filter(m => 
                        m.body && (
                            m.body.includes('sistema corrigido') ||
                            m.body.includes('API PADRÃO') ||
                            m.body.includes('TESTE COM API PADRÃO')
                        )
                    );
                    
                    if (relevantMessages.length > 0) {
                        console.log(`   🎉 MENSAGENS RELEVANTES ENCONTRADAS!`);
                        
                        relevantMessages.forEach(m => {
                            console.log(`      SID: ${m.sid}`);
                            console.log(`      Body: ${m.body}`);
                            console.log(`      Author: ${m.author}`);
                            console.log(`      Criada: ${m.dateCreated}`);
                        });
                        
                        console.log(`\n   🎯 ESTA É A CONVERSA ONDE AS MENSAGENS FUNCIONARAM!`);
                        console.log(`   📋 Configuração que funcionou:`);
                        
                        item.participantsWithProxy.forEach(p => {
                            console.log(`      Address: ${p.messagingBinding.address}`);
                            console.log(`      Proxy: ${p.messagingBinding.proxyAddress}`);
                        });
                        
                    } else {
                        console.log(`   📭 Nenhuma mensagem relevante encontrada`);
                    }
                    
                } catch (error) {
                    console.log(`   ❌ Erro ao verificar mensagens: ${error.message}`);
                }
            }
            
        } else {
            console.log(`\n❌ NENHUMA CONVERSA COM PROXY ENCONTRADA`);
            console.log(`   Isso significa que o proxy foi removido após as mensagens funcionarem`);
            console.log(`   ou que as mensagens funcionaram por outro motivo.`);
        }

    } catch (error) {
        console.error('❌ Erro na busca:', error.message);
    }
}

findWorkingConversation();
EOF

# Executar busca
node find_working_conversation.js

# Limpar arquivo temporário
rm find_working_conversation.js

echo ""
echo "🎯 PRÓXIMOS PASSOS:"
echo "=================="
echo "1. Se encontrarmos conversa com proxy, replicar a configuração"
echo "2. Se não encontrarmos, investigar logs de entrega"
echo "3. Testar a nova funcionalidade de encerramento de conversas"
echo ""
echo "🔧 Para testar encerramento:"
echo "   - Acesse: http://localhost:3000"
echo "   - Use os botões 'Encerrar' individuais"
echo "   - Use o botão 'Encerrar Todas' para limpar"
