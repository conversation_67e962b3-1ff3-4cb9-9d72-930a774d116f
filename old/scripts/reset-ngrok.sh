#!/bin/bash

# Script para limpar configurações antigas do ngrok
# Twilio WhatsApp Typing Indicator Test

echo "🧹 Limpando Configurações Antigas do ngrok"
echo "=========================================="

# Parar todos os processos ngrok
echo "🛑 Parando processos ngrok..."
pkill -f ngrok 2>/dev/null || true
sleep 2

# Remover arquivos de configuração antigos
echo "🗑️  Removendo arquivos de configuração..."

# Arquivo de configuração padrão do ngrok
NGROK_CONFIG_DIR="$HOME/.ngrok2"
if [ -d "$NGROK_CONFIG_DIR" ]; then
    echo "📁 Removendo diretório de configuração: $NGROK_CONFIG_DIR"
    rm -rf "$NGROK_CONFIG_DIR"
fi

# Arquivo de configuração local
if [ -f "ngrok.yml" ]; then
    echo "📄 Removendo ngrok.yml local"
    rm ngrok.yml
fi

# Arquivo de log
if [ -f "ngrok.log" ]; then
    echo "📄 Removendo ngrok.log"
    rm ngrok.log
fi

# Arquivo de PID
if [ -f "ngrok.pid" ]; then
    echo "📄 Removendo ngrok.pid"
    rm ngrok.pid
fi

# Arquivo de URLs
if [ -f "webhook-urls.txt" ]; then
    echo "📄 Removendo webhook-urls.txt antigo"
    rm webhook-urls.txt
fi

echo ""
echo "✅ Limpeza concluída!"
echo ""
echo "🔧 Próximos passos:"
echo "1. Execute: ./setup-ngrok-programmatic.sh"
echo "2. Configure os webhooks na Twilio"
echo "3. Teste a aplicação"
echo ""
echo "💡 O script irá configurar automaticamente com as credenciais do .env"
