#!/bin/bash

# Script para ajudar a obter o token correto do ngrok
# Twilio WhatsApp Typing Indicator Test

echo "🔑 Obtendo Token Correto do ngrok"
echo "================================="
echo ""
echo "O token fornecido está inválido. Vamos obter o token correto:"
echo ""
echo "📋 Passos para obter o token:"
echo "1. 🌐 Acesse: https://dashboard.ngrok.com/get-started/your-authtoken"
echo "2. 📧 Faça login com: <EMAIL>"
echo "3. 🔑 Copie o authtoken mostrado na página"
echo "4. 📝 Cole o token quando solicitado abaixo"
echo ""

# Abrir o navegador automaticamente (se disponível)
if command -v open &> /dev/null; then
    echo "🌐 Abrindo navegador..."
    open "https://dashboard.ngrok.com/get-started/your-authtoken"
    echo ""
fi

echo "⏳ Aguardando você obter o token..."
echo ""
echo "🔑 Cole o token do ngrok aqui:"
read -p "Token: " NEW_TOKEN

if [ -z "$NEW_TOKEN" ]; then
    echo "❌ Token não fornecido!"
    exit 1
fi

# Validar formato básico do token
if [[ ! "$NEW_TOKEN" =~ ^[a-zA-Z0-9_-]+$ ]]; then
    echo "❌ Formato do token parece inválido!"
    echo "💡 O token deve conter apenas letras, números, _ e -"
    exit 1
fi

echo ""
echo "✅ Token recebido: ${NEW_TOKEN:0:10}..."

# Atualizar arquivo .env
if [ -f .env ]; then
    # Fazer backup do .env
    cp .env .env.backup
    echo "💾 Backup criado: .env.backup"
    
    # Atualizar token no .env
    if grep -q "NGROK_AUTH_TOKEN=" .env; then
        # Substituir token existente
        sed -i.bak "s/NGROK_AUTH_TOKEN=.*/NGROK_AUTH_TOKEN=$NEW_TOKEN/" .env
        rm .env.bak
        echo "✅ Token atualizado no arquivo .env"
    else
        # Adicionar token se não existir
        echo "NGROK_AUTH_TOKEN=$NEW_TOKEN" >> .env
        echo "✅ Token adicionado ao arquivo .env"
    fi
else
    echo "❌ Arquivo .env não encontrado!"
    exit 1
fi

echo ""
echo "🧪 Testando novo token..."

# Limpar configurações antigas
./reset-ngrok.sh > /dev/null 2>&1

# Configurar novo token
ngrok config add-authtoken $NEW_TOKEN

if [ $? -eq 0 ]; then
    echo "✅ Token configurado com sucesso!"
    echo ""
    echo "🚀 Próximos passos:"
    echo "1. Execute: ./setup-ngrok-programmatic.sh"
    echo "2. Configure os webhooks na Twilio"
    echo "3. Teste a aplicação"
    echo ""
    echo "💡 Se ainda houver problemas, verifique se:"
    echo "   - <NAME_EMAIL> está ativa"
    echo "   - O email foi verificado no ngrok"
    echo "   - Você tem permissões para usar túneis"
else
    echo "❌ Falha ao configurar token!"
    echo "💡 Verifique se o token está correto"
fi
