#!/bin/bash

# Script de diagnóstico completo da configuração Twilio
# Verifica todas as configurações e identifica problemas

echo "🔍 DIAGNÓSTICO COMPLETO - Configuração Twilio"
echo "=============================================="

echo ""
echo "📋 1. Verificando Variáveis de Ambiente"
echo "========================================"

# Verificar .env
if [ -f .env ]; then
    echo "✅ Arquivo .env encontrado"
    
    # Verificar variáveis críticas (sem mostrar valores)
    if grep -q "TWILIO_ACCOUNT_SID=" .env; then
        echo "✅ TWILIO_ACCOUNT_SID configurado"
    else
        echo "❌ TWILIO_ACCOUNT_SID não encontrado"
    fi
    
    if grep -q "TWILIO_AUTH_TOKEN=" .env; then
        echo "✅ TWILIO_AUTH_TOKEN configurado"
    else
        echo "❌ TWILIO_AUTH_TOKEN não encontrado"
    fi
    
    if grep -q "TWILIO_CONVERSATIONS_SERVICE_SID=" .env; then
        echo "✅ TWILIO_CONVERSATIONS_SERVICE_SID configurado"
        SERVICE_SID=$(grep "TWILIO_CONVERSATIONS_SERVICE_SID=" .env | cut -d'=' -f2)
        echo "   Service SID: $SERVICE_SID"
    else
        echo "❌ TWILIO_CONVERSATIONS_SERVICE_SID não encontrado"
    fi
    
    if grep -q "TWILIO_WHATSAPP_SENDER_NUMBER=" .env; then
        echo "✅ TWILIO_WHATSAPP_SENDER_NUMBER configurado"
        SENDER_NUMBER=$(grep "TWILIO_WHATSAPP_SENDER_NUMBER=" .env | cut -d'=' -f2)
        echo "   Sender Number: $SENDER_NUMBER"
    else
        echo "❌ TWILIO_WHATSAPP_SENDER_NUMBER não encontrado"
    fi
    
else
    echo "❌ Arquivo .env não encontrado"
    exit 1
fi

echo ""
echo "📋 2. Testando Conexão com Twilio API"
echo "====================================="

# Criar script Node.js para testar API
cat > test_twilio_api.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function testTwilioAPI() {
    try {
        console.log('🔗 Testando conexão com Twilio API...');
        
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        // Testar Account
        console.log('📋 Verificando Account...');
        const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
        console.log(`✅ Account Status: ${account.status}`);
        console.log(`   Account SID: ${account.sid}`);
        console.log(`   Friendly Name: ${account.friendlyName}`);
        
        // Testar Conversations Service
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            console.log('');
            console.log('💬 Verificando Conversations Service...');
            const service = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .fetch();
            console.log(`✅ Service Status: ${service.friendlyName}`);
            console.log(`   Service SID: ${service.sid}`);
            console.log(`   Webhook URL: ${service.webhookUrl || 'N/A'}`);
            console.log(`   Webhook Method: ${service.webhookMethod || 'N/A'}`);
        }
        
        // Testar WhatsApp Sender
        console.log('');
        console.log('📱 Verificando WhatsApp Sender...');
        const senders = await client.messaging.v1.services.list();
        console.log(`📋 Total de Messaging Services: ${senders.length}`);
        
        for (const service of senders) {
            console.log(`   Service: ${service.friendlyName} (${service.sid})`);
            
            // Verificar phone numbers
            const phoneNumbers = await client.messaging.v1
                .services(service.sid)
                .phoneNumbers
                .list();
            
            for (const phone of phoneNumbers) {
                console.log(`      Phone: ${phone.phoneNumber} (${phone.capabilities})`);
                if (phone.phoneNumber === process.env.TWILIO_WHATSAPP_SENDER_NUMBER?.replace('whatsapp:', '')) {
                    console.log(`      ✅ WhatsApp Sender encontrado!`);
                }
            }
        }
        
        // Testar criação de participante diretamente
        console.log('');
        console.log('🧪 Testando criação de participante WhatsApp...');
        
        const conversationSid = 'CH329dd7f817714f1389025c01ddc6e248';
        const testNumber = 'whatsapp:+5551993590210';
        const proxyNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER;
        
        console.log(`   Conversation: ${conversationSid}`);
        console.log(`   Address: ${testNumber}`);
        console.log(`   Proxy: ${proxyNumber}`);
        
        try {
            let participant;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                participant = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(conversationSid)
                    .participants
                    .create({
                        'messagingBinding.address': testNumber,
                        'messagingBinding.proxyAddress': proxyNumber
                    });
            } else {
                participant = await client.conversations.v1
                    .conversations(conversationSid)
                    .participants
                    .create({
                        'messagingBinding.address': testNumber,
                        'messagingBinding.proxyAddress': proxyNumber
                    });
            }
            
            console.log(`✅ Participante criado com sucesso:`);
            console.log(`   SID: ${participant.sid}`);
            console.log(`   Address: ${participant.messagingBinding?.address}`);
            console.log(`   Proxy: ${participant.messagingBinding?.proxyAddress}`);
            console.log(`   Type: ${participant.messagingBinding?.type}`);
            
            // Verificar se proxy foi definido
            if (participant.messagingBinding?.proxyAddress) {
                console.log(`🎉 PROXY ADDRESS DEFINIDO CORRETAMENTE!`);
            } else {
                console.log(`❌ PROXY ADDRESS NÃO FOI DEFINIDO`);
                console.log(`   Isso indica um problema na configuração do Conversations Service`);
            }
            
        } catch (error) {
            if (error.code === 50416) {
                console.log(`⚠️  Participante já existe (esperado)`);
            } else {
                console.log(`❌ Erro ao criar participante: ${error.message}`);
                console.log(`   Code: ${error.code}`);
            }
        }
        
    } catch (error) {
        console.error('❌ Erro na API Twilio:', error.message);
        if (error.code) {
            console.error(`   Code: ${error.code}`);
        }
        process.exit(1);
    }
}

testTwilioAPI();
EOF

# Executar teste
node test_twilio_api.js

# Limpar arquivo temporário
rm test_twilio_api.js

echo ""
echo "📋 3. Verificando Configuração do Conversations Service"
echo "======================================================"

# Criar script para verificar configuração específica
cat > check_service_config.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function checkServiceConfig() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            console.log('🔍 Verificando configuração detalhada do Conversations Service...');
            
            const service = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .fetch();
            
            console.log(`📋 Configurações do Service:`);
            console.log(`   Friendly Name: ${service.friendlyName}`);
            console.log(`   SID: ${service.sid}`);
            console.log(`   Webhook URL: ${service.webhookUrl || 'N/A'}`);
            console.log(`   Webhook Method: ${service.webhookMethod || 'N/A'}`);
            console.log(`   Webhook Filters: ${service.webhookFilters || 'N/A'}`);
            console.log(`   Reachability Enabled: ${service.reachabilityEnabled}`);
            
            // Verificar configurações de binding
            const configuration = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .configuration()
                .fetch();
            
            console.log(`');
            console.log(`📋 Configurações de Binding:`);
            console.log(`   Default Chat Service Role: ${configuration.defaultChatServiceRole || 'N/A'}`);
            console.log(`   Default Conversation Creator Role: ${configuration.defaultConversationCreatorRole || 'N/A'}`);
            console.log(`   Default Conversation Role: ${configuration.defaultConversationRole || 'N/A'}`);
            console.log(`   Reachability Enabled: ${configuration.reachabilityEnabled}`);
            
        } else {
            console.log('⚠️  Usando Conversations API padrão (sem Service)');
            console.log('   Isso pode causar limitações na configuração de proxy');
        }
        
    } catch (error) {
        console.error('❌ Erro ao verificar configuração:', error.message);
    }
}

checkServiceConfig();
EOF

# Executar verificação
node check_service_config.js

# Limpar arquivo temporário
rm check_service_config.js

echo ""
echo "📋 4. Recomendações"
echo "=================="

echo "🔧 Baseado no diagnóstico:"
echo ""
echo "1. ✅ Se o Proxy Address não está sendo definido:"
echo "   - Problema pode estar na configuração do Conversations Service"
echo "   - Verifique se o WhatsApp Sender está vinculado ao Service"
echo "   - Considere usar Messaging Service em vez de número direto"
echo ""
echo "2. 🔄 Soluções alternativas:"
echo "   - Configurar Messaging Service na Twilio Console"
echo "   - Vincular WhatsApp Sender ao Messaging Service"
echo "   - Usar Messaging Service SID como proxy"
echo ""
echo "3. 📞 Contato Twilio Support:"
echo "   - Se problema persistir, contate o suporte Twilio"
echo "   - Mencione que proxyAddress não está sendo definido"
echo "   - Forneça SIDs de Service e Conversation"

echo ""
echo "🔍 Diagnóstico concluído!"
