#!/bin/bash

# Script para verificar participantes da conversa
# Identifica participantes duplicados ou com configuração incorreta

echo "🔍 Verificando Participantes da Conversa"
echo "========================================"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Configurações
CLIENT_NUMBER="+*************"
CONVERSATION_SID="CH329dd7f817714f1389025c01ddc6e248"

echo "📱 Cliente: $CLIENT_NUMBER"
echo "💬 Conversa: $CONVERSATION_SID"

echo ""
echo "🔍 Consultando participantes via Twilio API..."

# Usar Node.js para consultar participantes diretamente
cat > check_participants.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

async function checkParticipants() {
    try {
        const conversationSid = process.argv[2];
        
        console.log(`🔍 Consultando participantes da conversa: ${conversationSid}`);
        
        let participants;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participants = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .list();
        } else {
            participants = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .list();
        }

        console.log(`📋 Total de participantes: ${participants.length}`);
        console.log('');

        participants.forEach((participant, index) => {
            console.log(`👤 Participante ${index + 1}:`);
            console.log(`   SID: ${participant.sid}`);
            console.log(`   Identity: ${participant.identity || 'N/A'}`);
            console.log(`   Role: ${participant.roleSid || 'N/A'}`);
            
            if (participant.messagingBinding) {
                console.log(`   📱 Messaging Binding:`);
                console.log(`      Address: ${participant.messagingBinding.address || 'N/A'}`);
                console.log(`      Proxy Address: ${participant.messagingBinding.proxyAddress || 'N/A'}`);
                console.log(`      Type: ${participant.messagingBinding.type || 'N/A'}`);
            } else {
                console.log(`   📱 Messaging Binding: Nenhum`);
            }
            
            console.log(`   📅 Criado: ${participant.dateCreated}`);
            console.log(`   📅 Atualizado: ${participant.dateUpdated}`);
            console.log('');
        });

        // Identificar problemas
        const whatsappParticipants = participants.filter(p => 
            p.messagingBinding && p.messagingBinding.address && 
            p.messagingBinding.address.includes('+*************')
        );

        console.log(`🔍 Participantes WhatsApp para ${'+*************'}: ${whatsappParticipants.length}`);

        if (whatsappParticipants.length > 1) {
            console.log('⚠️  PROBLEMA: Múltiplos participantes WhatsApp encontrados!');
            
            whatsappParticipants.forEach((participant, index) => {
                console.log(`   ${index + 1}. SID: ${participant.sid}`);
                console.log(`      Proxy: ${participant.messagingBinding.proxyAddress}`);
                console.log(`      Criado: ${participant.dateCreated}`);
            });
            
            // Identificar qual usar (mais recente com proxy correto)
            const correctParticipant = whatsappParticipants.find(p => 
                p.messagingBinding.proxyAddress && 
                p.messagingBinding.proxyAddress.includes('+18382700077')
            );
            
            if (correctParticipant) {
                console.log(`✅ Participante correto encontrado: ${correctParticipant.sid}`);
                console.log(`   Proxy: ${correctParticipant.messagingBinding.proxyAddress}`);
            } else {
                console.log(`❌ Nenhum participante com proxy correto (+18382700077) encontrado`);
            }
            
            // Listar participantes para remoção
            const toRemove = whatsappParticipants.filter(p => 
                !p.messagingBinding.proxyAddress || 
                !p.messagingBinding.proxyAddress.includes('+18382700077')
            );
            
            if (toRemove.length > 0) {
                console.log(`🗑️  Participantes para remoção (${toRemove.length}):`);
                toRemove.forEach(p => {
                    console.log(`   - ${p.sid} (Proxy: ${p.messagingBinding.proxyAddress || 'N/A'})`);
                });
            }
        } else if (whatsappParticipants.length === 1) {
            const participant = whatsappParticipants[0];
            console.log(`✅ Um participante WhatsApp encontrado: ${participant.sid}`);
            console.log(`   Proxy: ${participant.messagingBinding.proxyAddress}`);
            
            if (participant.messagingBinding.proxyAddress && 
                participant.messagingBinding.proxyAddress.includes('+18382700077')) {
                console.log(`✅ Proxy correto configurado (+18382700077)`);
            } else {
                console.log(`❌ Proxy incorreto: ${participant.messagingBinding.proxyAddress}`);
                console.log(`   Deveria ser: whatsapp:+18382700077`);
            }
        } else {
            console.log(`❌ Nenhum participante WhatsApp encontrado para ${'+*************'}`);
        }

    } catch (error) {
        console.error('❌ Erro ao consultar participantes:', error.message);
    }
}

checkParticipants();
EOF

# Executar verificação
node check_participants.js "$CONVERSATION_SID"

# Limpar arquivo temporário
rm check_participants.js

echo ""
echo "🔧 Recomendações:"
echo "================"
echo "1. Se há múltiplos participantes WhatsApp, remova os antigos"
echo "2. Se o proxy está incorreto, remova e recrie o participante"
echo "3. Teste envio de mensagem após correção"
echo ""
echo "🛠️  Para corrigir automaticamente:"
echo "   ./fix-whatsapp-sender.sh"
