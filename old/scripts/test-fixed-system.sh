#!/bin/bash

# Teste do sistema corrigido
# Testa todas as funcionalidades com parâmetros corretos

echo "🔧 TESTE DO SISTEMA CORRIGIDO"
echo "============================="

# Reiniciar servidor para carregar mudanças
echo "🔄 Reiniciando servidor..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
sleep 2

cd /Users/<USER>/Documents/augment-projects/twiliowhats
npm start > server.log 2>&1 &
sleep 5

# Verificar se servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Falha ao iniciar servidor"
    echo "📋 Logs do servidor:"
    tail -10 server.log
    exit 1
fi

echo "✅ Servidor reiniciado com sucesso"

echo ""
echo "📋 1. Testando Encerramento de Conversas"
echo "========================================"

# Encerrar todas as conversas primeiro
echo "🔚 Encerrando todas as conversas..."
CLOSE_ALL=$(curl -s -X DELETE "http://localhost:3000/api/conversations/close-all/execute")
echo "📤 Resultado: $(echo "$CLOSE_ALL" | jq -r '.message' 2>/dev/null || echo "$CLOSE_ALL")"

echo ""
echo "📋 2. Testando Criação de Conversa com Parâmetros Corretos"
echo "=========================================================="

# Criar conversa com todos os parâmetros necessários
echo "📝 Criando conversa com parâmetros corretos..."
CREATE_CONV=$(curl -s -X POST "http://localhost:3000/api/conversations/connect-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "whatsappNumber": "+*************",
    "userIdentity": "operador-teste-corrigido",
    "friendlyName": "Teste Sistema Corrigido"
  }')

echo "📤 Resultado da criação:"
echo "$CREATE_CONV" | jq '.' 2>/dev/null || echo "$CREATE_CONV"

if echo "$CREATE_CONV" | grep -q '"success":true'; then
    CONV_SID=$(echo "$CREATE_CONV" | jq -r '.conversation.conversationSid' 2>/dev/null)
    PROXY_ADDR=$(echo "$CREATE_CONV" | jq -r '.participants.whatsapp.data.proxyAddress' 2>/dev/null)
    
    echo ""
    echo "✅ Conversa criada com sucesso!"
    echo "   SID: $CONV_SID"
    echo "   Proxy: $PROXY_ADDR"
    
    if [ "$PROXY_ADDR" != "null" ] && [ "$PROXY_ADDR" != "" ]; then
        echo "🎉 PROXY ADDRESS FUNCIONANDO!"
        
        # Verificar qual proxy foi usado
        if [[ "$PROXY_ADDR" == *"+***********"* ]]; then
            echo "📱 Usando: Número Registrado (+***********)"
        elif [[ "$PROXY_ADDR" == *"+14155238886"* ]]; then
            echo "📱 Usando: Sandbox (+14155238886) - FALLBACK"
        fi
        
        echo ""
        echo "💬 Testando envio de mensagem..."
        
        # Enviar mensagem
        SEND_MSG=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONV_SID/messages" \
          -H "Content-Type: application/json" \
          -d '{
            "body": "🎉 SISTEMA CORRIGIDO FUNCIONANDO! Esta mensagem deve chegar no WhatsApp!",
            "author": "operador-teste-corrigido"
          }')
        
        echo "📤 Resultado do envio:"
        echo "$SEND_MSG" | jq '.' 2>/dev/null || echo "$SEND_MSG"
        
        if echo "$SEND_MSG" | grep -q '"messageSid"'; then
            MSG_SID=$(echo "$SEND_MSG" | jq -r '.messageSid' 2>/dev/null)
            echo ""
            echo "🎉 MENSAGEM ENVIADA COM SUCESSO!"
            echo "📝 Message SID: $MSG_SID"
            echo "📱 Proxy: $PROXY_ADDR"
            echo ""
            echo "⏳ Aguarde alguns segundos e verifique se chegou no WhatsApp!"
            
            # Monitorar logs por 10 segundos
            echo ""
            echo "📊 Monitorando logs por 10 segundos..."
            for i in {1..10}; do
                sleep 1
                
                # Verificar logs da mensagem
                if grep -q "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                    echo ""
                    echo "📋 Logs da mensagem encontrados:"
                    grep "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -2
                    break
                fi
                
                echo -n "."
            done
            
        else
            echo "❌ Falha ao enviar mensagem"
        fi
        
        echo ""
        echo "🔚 Testando encerramento individual..."
        
        # Testar encerramento da conversa criada
        CLOSE_CONV=$(curl -s -X DELETE "http://localhost:3000/api/conversations/$CONV_SID")
        
        if echo "$CLOSE_CONV" | grep -q '"message"'; then
            echo "✅ Conversa encerrada com sucesso"
            echo "📋 $(echo "$CLOSE_CONV" | jq -r '.message' 2>/dev/null)"
        else
            echo "❌ Falha ao encerrar conversa"
            echo "📋 $CLOSE_CONV"
        fi
        
    else
        echo "❌ Proxy Address não foi definido"
        echo "🔍 Verificando logs do servidor..."
        tail -5 server.log
    fi
    
else
    echo "❌ Falha ao criar conversa"
    echo "📋 Erro: $(echo "$CREATE_CONV" | jq -r '.error' 2>/dev/null || echo "$CREATE_CONV")"
fi

echo ""
echo "📋 3. Testando Webhook Corrigido"
echo "==============================="

# Testar webhook com rota correta
echo "📱 Testando webhook com rota correta..."
WEBHOOK_TEST=$(curl -s -X POST "http://localhost:3000/webhooks/whatsapp/incoming" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+*************&To=whatsapp:+***********&Body=Teste%20webhook%20corrigido&MessageSid=TEST_WEBHOOK_$(date +%s)&AccountSid=TEST&ApiVersion=2010-04-01")

echo "📤 Resposta do webhook: $WEBHOOK_TEST"

# Verificar se criou conversa automática
sleep 3
echo ""
echo "🔍 Verificando conversas após webhook..."
CONV_AFTER=$(curl -s http://localhost:3000/api/conversations)
CONV_COUNT=$(echo "$CONV_AFTER" | jq '.conversations | length' 2>/dev/null || echo "0")
echo "📋 Conversas ativas: $CONV_COUNT"

echo ""
echo "📋 4. Verificando Logs Recentes"
echo "==============================="

echo "📄 Últimos 5 logs do sistema:"
if [ -f "logs/twilio-$(date +%Y-%m-%d).log" ]; then
    tail -5 "logs/twilio-$(date +%Y-%m-%d).log" | while read line; do
        echo "   $line"
    done
else
    echo "❌ Arquivo de log não encontrado"
fi

echo ""
echo "📋 RESUMO DOS TESTES"
echo "==================="

echo "🔧 Correções aplicadas:"
echo "✅ Variável 'senderNumber' corrigida no webhook"
echo "✅ Parâmetros obrigatórios adicionados aos testes"
echo "✅ Rota de webhook corrigida"
echo "✅ Sistema de fallback implementado"

echo ""
echo "🎯 Resultados esperados:"
echo "1. ✅ Conversas sendo encerradas corretamente"
echo "2. ✅ Proxy address sendo definido (registrado ou sandbox)"
echo "3. ✅ Mensagens sendo enviadas"
echo "4. ✅ Webhooks funcionando"
echo "5. ✅ Logs sendo gerados"

echo ""
echo "📱 TESTE MANUAL FINAL:"
echo "====================="
echo "1. Envie mensagem WhatsApp para: +***********"
echo "2. Acesse: http://localhost:3000"
echo "3. Conecte como operador"
echo "4. Veja a conversa aparecer automaticamente"
echo "5. Responda através da interface"

echo ""
echo "🔍 Para monitorar logs em tempo real:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"

echo ""
echo "🌐 Interface disponível em: http://localhost:3000"
