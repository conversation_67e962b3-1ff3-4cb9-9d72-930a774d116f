#!/bin/bash

# Teste direto de criação de participante
# Criar conversa e adicionar participante separadamente

echo "🧪 TESTE DIRETO - Criação de Participante"
echo "========================================="

CLIENT_NUMBER="+5551993590210"

echo "📱 Cliente: $CLIENT_NUMBER"

echo ""
echo "📝 Criando nova conversa simples..."

# Criar conversa simples
CREATE_CONV=$(curl -s -X POST "http://localhost:3000/api/conversations" \
  -H "Content-Type: application/json" \
  -d '{"friendlyName":"Teste Híbrido Final","uniqueName":"teste-hibrido-final"}')

echo "📤 Resultado da criação da conversa:"
echo "$CREATE_CONV" | jq '.' 2>/dev/null || echo "$CREATE_CONV"

if echo "$CREATE_CONV" | grep -q '"conversationSid"'; then
    CONVERSATION_SID=$(echo "$CREATE_CONV" | jq -r '.conversationSid' 2>/dev/null)
    
    echo ""
    echo "✅ Conversa criada: $CONVERSATION_SID"
    
    echo ""
    echo "👤 Adicionando participante WhatsApp com sistema híbrido..."
    
    # Adicionar participante WhatsApp
    ADD_PARTICIPANT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/whatsapp" \
      -H "Content-Type: application/json" \
      -d '{"whatsappNumber":"'"$CLIENT_NUMBER"'"}')
    
    echo "📤 Resultado da adição do participante:"
    echo "$ADD_PARTICIPANT" | jq '.' 2>/dev/null || echo "$ADD_PARTICIPANT"
    
    if echo "$ADD_PARTICIPANT" | grep -q '"participantSid"'; then
        PARTICIPANT_SID=$(echo "$ADD_PARTICIPANT" | jq -r '.participantSid' 2>/dev/null)
        PROXY_ADDRESS=$(echo "$ADD_PARTICIPANT" | jq -r '.proxyAddress' 2>/dev/null)
        
        echo ""
        echo "✅ Participante criado:"
        echo "   SID: $PARTICIPANT_SID"
        echo "   Proxy: $PROXY_ADDRESS"
        
        if [ "$PROXY_ADDRESS" != "null" ] && [ "$PROXY_ADDRESS" != "" ]; then
            echo ""
            echo "🎉 SUCESSO! Proxy Address definido!"
            
            # Verificar qual proxy foi usado
            if [[ "$PROXY_ADDRESS" == *"+18382700077"* ]]; then
                echo "📱 Proxy usado: Número Registrado (+18382700077)"
            elif [[ "$PROXY_ADDRESS" == *"+14155238886"* ]]; then
                echo "📱 Proxy usado: Sandbox (+14155238886) - FALLBACK FUNCIONOU!"
            else
                echo "📱 Proxy usado: $PROXY_ADDRESS"
            fi
            
            echo ""
            echo "👨‍💼 Adicionando operador..."
            
            # Adicionar operador
            ADD_OPERATOR=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
              -H "Content-Type: application/json" \
              -d '{"identity":"operador-teste-final"}')
            
            echo "📤 Operador adicionado:"
            echo "$ADD_OPERATOR" | jq '.' 2>/dev/null || echo "$ADD_OPERATOR"
            
            sleep 2
            
            echo ""
            echo "💬 Enviando mensagem de teste..."
            
            # Enviar mensagem
            SEND_MSG=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
              -H "Content-Type: application/json" \
              -d '{"body":"🎉 TESTE FINAL HÍBRIDO! Proxy: '"$PROXY_ADDRESS"' - Mensagem deve chegar no WhatsApp!","author":"operador-teste-final"}')
            
            echo "📤 Resultado do envio:"
            echo "$SEND_MSG" | jq '.' 2>/dev/null || echo "$SEND_MSG"
            
            if echo "$SEND_MSG" | grep -q '"messageSid"'; then
                MESSAGE_SID=$(echo "$SEND_MSG" | jq -r '.messageSid' 2>/dev/null)
                
                echo ""
                echo "🎉 MENSAGEM ENVIADA COM SUCESSO!"
                echo "📝 Message SID: $MESSAGE_SID"
                echo "📱 Proxy: $PROXY_ADDRESS"
                echo "💬 Conversa: $CONVERSATION_SID"
                echo ""
                echo "⏳ Aguarde alguns segundos e verifique se chegou no WhatsApp!"
                echo ""
                echo "🎯 SISTEMA HÍBRIDO FUNCIONANDO!"
                echo "==============================="
                echo "✅ Fallback implementado com sucesso"
                echo "✅ Proxy Address definido corretamente"
                echo "✅ Mensagem enviada"
                echo ""
                echo "📱 Verifique seu WhatsApp ($CLIENT_NUMBER)"
                
            else
                echo "❌ Falha ao enviar mensagem"
            fi
            
        else
            echo "❌ Proxy Address ainda não definido"
        fi
        
    else
        echo "❌ Falha ao adicionar participante"
        echo "$ADD_PARTICIPANT"
    fi
    
else
    echo "❌ Falha ao criar conversa"
    echo "$CREATE_CONV"
fi

echo ""
echo "🔍 Verificando logs do servidor..."
echo "Últimas 10 linhas dos logs:"
tail -10 logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "Nenhum log encontrado"
