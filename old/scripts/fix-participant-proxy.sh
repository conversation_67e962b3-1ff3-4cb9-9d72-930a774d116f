#!/bin/bash

# Script para corrigir o Proxy Address do participante WhatsApp
# Remove participante sem proxy e cria novo com configuração correta

echo "🔧 Corrigindo Proxy Address do Participante WhatsApp"
echo "===================================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Configurações
CLIENT_NUMBER="+5551993590210"
CONVERSATION_SID="CH329dd7f817714f1389025c01ddc6e248"
PARTICIPANT_SID="MB2c2d233f96c342b5a56f657f1fa32451"

echo "📱 Cliente: $CLIENT_NUMBER"
echo "💬 Conversa: $CONVERSATION_SID"
echo "👤 Participante problemático: $PARTICIPANT_SID"

echo ""
echo "🗑️  Removendo participante com proxy incorreto..."

# Criar script Node.js para remover participante
cat > remove_participant.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

async function removeParticipant() {
    try {
        const conversationSid = process.argv[2];
        const participantSid = process.argv[3];
        
        console.log(`🗑️  Removendo participante ${participantSid} da conversa ${conversationSid}`);
        
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants(participantSid)
                .remove();
        } else {
            await client.conversations.v1
                .conversations(conversationSid)
                .participants(participantSid)
                .remove();
        }

        console.log(`✅ Participante removido com sucesso`);

    } catch (error) {
        console.error('❌ Erro ao remover participante:', error.message);
        process.exit(1);
    }
}

removeParticipant();
EOF

# Executar remoção
node remove_participant.js "$CONVERSATION_SID" "$PARTICIPANT_SID"

# Limpar arquivo temporário
rm remove_participant.js

if [ $? -eq 0 ]; then
    echo "✅ Participante removido com sucesso"
    
    echo ""
    echo "⏳ Aguardando 3 segundos..."
    sleep 3
    
    echo ""
    echo "➕ Adicionando novo participante com proxy correto..."
    
    # Adicionar participante com configuração correta
    ADD_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/whatsapp" \
      -H "Content-Type: application/json" \
      -d "{\"whatsappNumber\":\"$CLIENT_NUMBER\"}")
    
    echo "📤 Resultado da adição:"
    echo "$ADD_RESULT" | jq '.' 2>/dev/null || echo "$ADD_RESULT"
    
    # Verificar se foi adicionado com sucesso
    if echo "$ADD_RESULT" | grep -q "participantSid"; then
        NEW_PARTICIPANT_SID=$(echo "$ADD_RESULT" | jq -r '.participantSid' 2>/dev/null)
        PROXY_ADDRESS=$(echo "$ADD_RESULT" | jq -r '.proxyAddress' 2>/dev/null)
        
        echo "✅ Novo participante criado: $NEW_PARTICIPANT_SID"
        echo "📱 Proxy Address: $PROXY_ADDRESS"
        
        if [[ "$PROXY_ADDRESS" == *"+18382700077"* ]]; then
            echo "🎉 SUCESSO! Proxy Address configurado corretamente!"
            
            echo ""
            echo "💬 Testando envio de mensagem..."
            
            # Adicionar operador se necessário
            curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
              -H "Content-Type: application/json" \
              -d '{"identity":"operador-teste-final"}' > /dev/null
            
            sleep 2
            
            # Enviar mensagem de teste
            SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
              -H "Content-Type: application/json" \
              -d '{"body":"🎉 TESTE FINAL - Proxy corrigido! Esta mensagem deve chegar no WhatsApp.","author":"operador-teste-final"}')
            
            echo "📤 Resultado do envio:"
            echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"
            
            if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
                MESSAGE_SID=$(echo "$SEND_RESULT" | jq -r '.messageSid' 2>/dev/null)
                echo ""
                echo "🎉 MENSAGEM ENVIADA COM SUCESSO!"
                echo "📝 Message SID: $MESSAGE_SID"
                echo "📱 Proxy Address: $PROXY_ADDRESS"
                echo ""
                echo "⏳ Aguardando 15 segundos para verificar entrega..."
                
                # Monitorar logs por 15 segundos
                echo "📊 Monitorando logs de entrega..."
                
                for i in {1..15}; do
                    sleep 1
                    
                    # Verificar se há logs de status para esta mensagem
                    if grep -q "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                        echo "📋 Status encontrado para mensagem $MESSAGE_SID:"
                        grep "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -3
                        break
                    fi
                    
                    # Verificar logs de status WhatsApp recentes
                    RECENT_STATUS=$(grep -E "(sent|delivered|read)" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null | tail -1)
                    if [[ "$RECENT_STATUS" == *"$(date +%Y-%m-%d)"* ]]; then
                        echo "📱 Status WhatsApp recente encontrado:"
                        echo "$RECENT_STATUS" | jq '.body.MessageStatus' 2>/dev/null || echo "Status não disponível"
                    fi
                    
                    echo -n "."
                done
                
                echo ""
                echo ""
                echo "📋 Resumo Final:"
                echo "==============="
                echo "✅ Participante antigo removido"
                echo "✅ Novo participante criado com proxy correto"
                echo "✅ Mensagem enviada com sucesso"
                echo "📱 Proxy: $PROXY_ADDRESS"
                echo "💬 Message SID: $MESSAGE_SID"
                echo ""
                echo "🎯 Agora teste manualmente:"
                echo "1. 📱 Verifique se a mensagem chegou no WhatsApp"
                echo "2. 🌐 Acesse http://localhost:3000"
                echo "3. 👨‍💼 Conecte como operador"
                echo "4. 💬 Responda mensagens através da interface"
                
            else
                echo "❌ Falha ao enviar mensagem de teste"
            fi
            
        else
            echo "❌ Proxy Address ainda incorreto: $PROXY_ADDRESS"
        fi
        
    else
        echo "❌ Falha ao adicionar novo participante"
        echo "$ADD_RESULT"
    fi
    
else
    echo "❌ Falha ao remover participante antigo"
fi

echo ""
echo "🔍 Para monitorar logs em tempo real:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
