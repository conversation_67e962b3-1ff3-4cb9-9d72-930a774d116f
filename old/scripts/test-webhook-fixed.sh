#!/bin/bash

# Teste do webhook corrigido
# Simula mensagem do WhatsApp para criar conversa automática

echo "🔧 TESTE - Webhook Corrigido para Criação Automática"
echo "===================================================="

# Reiniciar servidor para carregar mudanças
echo "🔄 Reiniciando servidor com webhook corrigido..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
sleep 2

cd /Users/<USER>/Documents/augment-projects/twiliowhats
npm start > server.log 2>&1 &
sleep 5

if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Falha ao iniciar servidor"
    exit 1
fi

echo "✅ Servidor reiniciado"

echo ""
echo "📋 1. Limpando Estado Anterior"
echo "============================="

# Encerrar todas as conversas
echo "🔚 Encerrando todas as conversas..."
CLOSE_ALL=$(curl -s -X DELETE "http://localhost:3000/api/conversations/close-all/execute")
CLOSED_COUNT=$(echo "$CLOSE_ALL" | jq -r '.successCount' 2>/dev/null || echo "0")
echo "✅ $CLOSED_COUNT conversas encerradas"

echo ""
echo "📋 2. Testando Webhook com Criação Automática"
echo "============================================="

# Simular mensagem do WhatsApp que deve criar conversa automática
echo "📱 Simulando mensagem do WhatsApp..."

WEBHOOK_TEST=$(curl -s -X POST "http://localhost:3000/webhooks/whatsapp/incoming" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+*************&To=whatsapp:+***********&Body=Oi%2C%20preciso%20de%20ajuda&MessageSid=TEST_AUTO_$(date +%s)&AccountSid=AC04266eaa4a7caf821e8dac9d92879e95&ApiVersion=2010-04-01")

echo "📤 Resposta do webhook: $WEBHOOK_TEST"

# Aguardar processamento
echo ""
echo "⏳ Aguardando 5 segundos para processamento..."
sleep 5

# Verificar se conversa foi criada
echo ""
echo "🔍 Verificando se conversa foi criada automaticamente..."

CONV_LIST=$(curl -s http://localhost:3000/api/conversations)
CONV_COUNT=$(echo "$CONV_LIST" | jq '.conversations | length' 2>/dev/null || echo "0")

echo "📋 Conversas ativas após webhook: $CONV_COUNT"

if [ "$CONV_COUNT" -gt 0 ]; then
    echo ""
    echo "🎉 CONVERSA CRIADA AUTOMATICAMENTE!"
    
    # Mostrar detalhes da conversa
    CONV_DETAILS=$(echo "$CONV_LIST" | jq '.conversations[0]' 2>/dev/null)
    CONV_SID=$(echo "$CONV_DETAILS" | jq -r '.sid' 2>/dev/null)
    CONV_NAME=$(echo "$CONV_DETAILS" | jq -r '.friendlyName' 2>/dev/null)
    
    echo "📋 Detalhes da conversa:"
    echo "   SID: $CONV_SID"
    echo "   Nome: $CONV_NAME"
    
    # Verificar participantes
    echo ""
    echo "👥 Verificando participantes..."
    
    PARTICIPANTS=$(curl -s "http://localhost:3000/api/conversations/$CONV_SID/participants")
    echo "📤 Participantes:"
    echo "$PARTICIPANTS" | jq '.' 2>/dev/null || echo "$PARTICIPANTS"
    
    # Testar resposta do operador
    echo ""
    echo "💬 Testando resposta do operador..."
    
    # Adicionar operador
    ADD_OP=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONV_SID/participants/chat" \
      -H "Content-Type: application/json" \
      -d '{"identity":"operador-auto"}')
    
    if echo "$ADD_OP" | grep -q '"participantSid"'; then
        echo "✅ Operador adicionado"
        
        sleep 2
        
        # Enviar resposta
        SEND_REPLY=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONV_SID/messages" \
          -H "Content-Type: application/json" \
          -d '{"body":"Olá! Recebi sua mensagem. Como posso ajudar?","author":"operador-auto"}')
        
        if echo "$SEND_REPLY" | grep -q '"messageSid"'; then
            MSG_SID=$(echo "$SEND_REPLY" | jq -r '.messageSid' 2>/dev/null)
            echo "✅ Resposta enviada: $MSG_SID"
            
            echo ""
            echo "⏳ Monitorando entrega por 10 segundos..."
            
            # Monitorar logs
            for i in {1..10}; do
                sleep 1
                
                # Verificar logs da mensagem
                if grep -q "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                    echo ""
                    echo "📋 Logs da mensagem encontrados:"
                    grep "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -2
                    break
                fi
                
                echo -n "."
            done
            
        else
            echo "❌ Falha ao enviar resposta"
            echo "$SEND_REPLY"
        fi
        
    else
        echo "❌ Falha ao adicionar operador"
        echo "$ADD_OP"
    fi
    
else
    echo "❌ Nenhuma conversa foi criada automaticamente"
    echo ""
    echo "🔍 Verificando logs de erro..."
    
    # Verificar logs de erro recentes
    echo "📋 Últimos logs do servidor:"
    tail -10 server.log
    
    echo ""
    echo "📋 Últimos logs do sistema:"
    tail -5 logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "Nenhum log encontrado"
fi

echo ""
echo "📋 3. Testando Mensagem Direta como Alternativa"
echo "=============================================="

# Se a criação automática falhou, testar mensagem direta
echo "📱 Testando mensagem direta como alternativa..."

DIRECT_MSG=$(curl -s -X POST "http://localhost:3000/api/conversations/send-direct-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "+*************",
    "body": "🔧 Teste de mensagem direta - Sistema funcionando como alternativa!"
  }')

echo "📤 Resultado da mensagem direta:"
echo "$DIRECT_MSG" | jq '.' 2>/dev/null || echo "$DIRECT_MSG"

if echo "$DIRECT_MSG" | grep -q '"success":true'; then
    MSG_SID=$(echo "$DIRECT_MSG" | jq -r '.messageSid' 2>/dev/null)
    SENDER=$(echo "$DIRECT_MSG" | jq -r '.sender' 2>/dev/null)
    
    echo ""
    echo "✅ Mensagem direta enviada com sucesso!"
    echo "📝 Message SID: $MSG_SID"
    echo "📱 Sender: $SENDER"
    
else
    echo "❌ Falha na mensagem direta"
fi

echo ""
echo "📋 RESUMO DO TESTE"
echo "=================="

if [ "$CONV_COUNT" -gt 0 ]; then
    echo "🎉 WEBHOOK FUNCIONANDO!"
    echo "✅ Conversa criada automaticamente"
    echo "✅ Sistema reativo funcionando"
    echo "✅ Participantes adicionados com fallback"
    echo ""
    echo "📱 TESTE MANUAL:"
    echo "==============="
    echo "1. Envie mensagem WhatsApp para: +***********"
    echo "2. Acesse: http://localhost:3000"
    echo "3. Veja a conversa aparecer automaticamente"
    echo "4. Responda através da interface"
    
else
    echo "⚠️ WEBHOOK COM PROBLEMAS"
    echo "❌ Conversa não foi criada automaticamente"
    echo "✅ Mensagem direta funcionando como alternativa"
    echo ""
    echo "🔧 SOLUÇÕES:"
    echo "============"
    echo "1. Use o botão 'Testar WhatsApp' na interface"
    echo "2. Crie conversas manualmente se necessário"
    echo "3. Sistema de mensagem direta está funcionando"
fi

echo ""
echo "🔍 Para monitorar logs:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"

echo ""
echo "🌐 Interface disponível em: http://localhost:3000"
