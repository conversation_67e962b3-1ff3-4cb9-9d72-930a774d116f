#!/bin/bash

# Script de desenvolvimento para Mac
# Twi<PERSON>s<PERSON>pp Typing Indicator Test

echo "🛠️  Modo Desenvolvimento - Mac"
echo "==============================="

# Verificar se nodemon está instalado
if ! command -v npx &> /dev/null; then
    echo "❌ npx não encontrado!"
    exit 1
fi

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
fi

echo "🔄 Iniciando em modo desenvolvimento com auto-reload..."
echo "📱 Aplicação: http://localhost:3000"
echo "🔧 Logs: Acompanhe as mudanças em tempo real"
echo "🛑 Para parar: Ctrl+C"
echo ""

# Abrir navegador automaticamente (opcional)
if command -v open &> /dev/null; then
    echo "🌐 Abrindo navegador..."
    sleep 2 && open http://localhost:3000 &
fi

# Iniciar em modo desenvolvimento
npm run dev
