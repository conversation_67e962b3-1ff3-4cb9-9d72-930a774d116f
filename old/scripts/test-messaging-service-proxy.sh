#!/bin/bash

# Script para testar Messaging Service SID como proxy
# Solução final para o problema do proxy address

echo "🔧 TESTE FINAL - Messaging Service SID como Proxy"
echo "================================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Configurações
CLIENT_NUMBER="+5551993590210"
CONVERSATION_SID="CH329dd7f817714f1389025c01ddc6e248"
MESSAGING_SERVICE_SID="MG9af79f38b074003401bb1ac6f9a5cc17"

echo "📱 Cliente: $CLIENT_NUMBER"
echo "💬 Conversa: $CONVERSATION_SID"
echo "📨 Messaging Service: $MESSAGING_SERVICE_SID"

echo ""
echo "🧹 Limpando participantes WhatsApp antigos..."

# Limpar participantes antigos
curl -s -X DELETE "http://localhost:3000/api/conversations/cleanup-whatsapp/$CLIENT_NUMBER" > /dev/null

echo "✅ Participantes antigos removidos"

echo ""
echo "⏳ Aguardando 3 segundos..."
sleep 3

echo ""
echo "🧪 Testando criação com Messaging Service SID..."

# Criar script Node.js para testar com Messaging Service
cat > test_messaging_service.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function testMessagingService() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        const conversationSid = process.argv[2];
        const clientNumber = process.argv[3];
        const formattedNumber = clientNumber.startsWith('whatsapp:') ? clientNumber : `whatsapp:${clientNumber}`;
        const messagingServiceSid = process.env.TWILIO_MESSAGING_SERVICE_SID;
        
        console.log(`🧪 Testando criação de participante:`);
        console.log(`   Conversation: ${conversationSid}`);
        console.log(`   Address: ${formattedNumber}`);
        console.log(`   Messaging Service SID: ${messagingServiceSid}`);
        
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': messagingServiceSid
                });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': messagingServiceSid
                });
        }

        console.log(`✅ Participante criado com sucesso:`);
        console.log(`   SID: ${participant.sid}`);
        console.log(`   Address: ${participant.messagingBinding?.address}`);
        console.log(`   Proxy Address: ${participant.messagingBinding?.proxyAddress}`);
        console.log(`   Type: ${participant.messagingBinding?.type}`);
        
        // Verificar se proxy foi definido corretamente
        if (participant.messagingBinding?.proxyAddress === messagingServiceSid) {
            console.log(`🎉 SUCESSO! Messaging Service SID definido como proxy!`);
            console.log(`RESULT=SUCCESS`);
            console.log(`PARTICIPANT_SID=${participant.sid}`);
            console.log(`PROXY_ADDRESS=${participant.messagingBinding.proxyAddress}`);
        } else if (participant.messagingBinding?.proxyAddress) {
            console.log(`⚠️  Proxy definido mas diferente do esperado:`);
            console.log(`   Esperado: ${messagingServiceSid}`);
            console.log(`   Recebido: ${participant.messagingBinding.proxyAddress}`);
            console.log(`RESULT=PARTIAL`);
            console.log(`PARTICIPANT_SID=${participant.sid}`);
            console.log(`PROXY_ADDRESS=${participant.messagingBinding.proxyAddress}`);
        } else {
            console.log(`❌ Proxy Address ainda não foi definido`);
            console.log(`RESULT=FAILED`);
            console.log(`PARTICIPANT_SID=${participant.sid}`);
            console.log(`PROXY_ADDRESS=N/A`);
        }

    } catch (error) {
        if (error.code === 50416) {
            console.log(`⚠️  Participante já existe - removendo e tentando novamente...`);
            console.log(`RESULT=EXISTS`);
        } else {
            console.error(`❌ Erro ao criar participante: ${error.message}`);
            console.error(`   Code: ${error.code}`);
            console.log(`RESULT=ERROR`);
        }
    }
}

testMessagingService();
EOF

# Executar teste
TEST_OUTPUT=$(node test_messaging_service.js "$CONVERSATION_SID" "$CLIENT_NUMBER")
TEST_RESULT=$?

# Limpar arquivo temporário
rm test_messaging_service.js

echo "$TEST_OUTPUT"

# Extrair resultado
RESULT=$(echo "$TEST_OUTPUT" | grep "RESULT=" | cut -d'=' -f2)
PARTICIPANT_SID=$(echo "$TEST_OUTPUT" | grep "PARTICIPANT_SID=" | cut -d'=' -f2)
PROXY_ADDRESS=$(echo "$TEST_OUTPUT" | grep "PROXY_ADDRESS=" | cut -d'=' -f2)

echo ""
echo "📋 Resultado do Teste:"
echo "====================="
echo "🎯 Status: $RESULT"
echo "👤 Participant SID: $PARTICIPANT_SID"
echo "📨 Proxy Address: $PROXY_ADDRESS"

if [ "$RESULT" = "SUCCESS" ]; then
    echo ""
    echo "🎉 SUCESSO! Messaging Service SID funcionou como proxy!"
    echo ""
    echo "💬 Testando envio de mensagem..."
    
    # Adicionar operador
    curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
      -H "Content-Type: application/json" \
      -d '{"identity":"operador-final-test"}' > /dev/null 2>&1
    
    sleep 2
    
    # Enviar mensagem de teste
    SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
      -H "Content-Type: application/json" \
      -d '{"body":"🎉 TESTE FINAL COM MESSAGING SERVICE! Esta mensagem deve chegar no WhatsApp usando proxy: '"$PROXY_ADDRESS"'","author":"operador-final-test"}')
    
    echo "📤 Resultado do envio:"
    echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"
    
    if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
        MESSAGE_SID=$(echo "$SEND_RESULT" | jq -r '.messageSid' 2>/dev/null)
        echo ""
        echo "🎉 MENSAGEM ENVIADA COM MESSAGING SERVICE PROXY!"
        echo "📝 Message SID: $MESSAGE_SID"
        echo "📨 Messaging Service: $PROXY_ADDRESS"
        echo ""
        echo "⏳ Monitorando entrega por 30 segundos..."
        
        # Monitorar logs
        for i in {1..30}; do
            sleep 1
            
            # Verificar logs específicos da mensagem
            if grep -q "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                echo ""
                echo "📋 Logs da mensagem encontrados:"
                grep "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -3
                break
            fi
            
            # Verificar se há novos logs de entrega
            RECENT_LOG=$(tail -3 logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null | grep -E "(delivered|sent|failed)" | tail -1)
            if [[ "$RECENT_LOG" == *"$(date +%Y-%m-%d)"* ]]; then
                TIMESTAMP=$(echo "$RECENT_LOG" | jq -r '.timestamp' 2>/dev/null)
                if [[ "$TIMESTAMP" > "$(date -u -v-60s +%Y-%m-%dT%H:%M:%S)" ]]; then
                    echo ""
                    echo "📱 Log de entrega recente:"
                    echo "$RECENT_LOG" | jq '.' 2>/dev/null || echo "$RECENT_LOG"
                    break
                fi
            fi
            
            echo -n "."
        done
        
        echo ""
        echo ""
        echo "📋 RESUMO FINAL:"
        echo "==============="
        echo "✅ Problema do proxy address RESOLVIDO!"
        echo "✅ Messaging Service SID funcionando como proxy"
        echo "✅ Mensagem enviada com sucesso"
        echo "📨 Proxy: $PROXY_ADDRESS"
        echo "💬 Message SID: $MESSAGE_SID"
        echo ""
        echo "🎯 TESTE MANUAL FINAL:"
        echo "====================="
        echo "1. 📱 Verifique se a mensagem chegou no WhatsApp ($CLIENT_NUMBER)"
        echo "2. 📱 Responda no WhatsApp para testar fluxo completo"
        echo "3. 🌐 Use a interface web para responder: http://localhost:3000"
        echo ""
        echo "✅ Sistema de chat WhatsApp totalmente funcional!"
        
    else
        echo "❌ Falha ao enviar mensagem de teste"
    fi
    
elif [ "$RESULT" = "PARTIAL" ]; then
    echo ""
    echo "⚠️  Proxy definido mas diferente do esperado"
    echo "🔧 Pode ainda funcionar - teste manual necessário"
    
elif [ "$RESULT" = "EXISTS" ]; then
    echo ""
    echo "⚠️  Participante já existe - limpando e tentando novamente..."
    
    # Limpar novamente e tentar
    curl -s -X DELETE "http://localhost:3000/api/conversations/cleanup-whatsapp/$CLIENT_NUMBER" > /dev/null
    sleep 3
    
    echo "🔄 Tentando novamente após limpeza..."
    # Recursão simples - executar novamente
    exec "$0"
    
else
    echo ""
    echo "❌ Teste falhou - problema persiste"
    echo "🔧 Soluções alternativas necessárias"
fi

echo ""
echo "🔍 Para monitorar logs:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
