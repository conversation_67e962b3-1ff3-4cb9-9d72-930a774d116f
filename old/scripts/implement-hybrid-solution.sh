#!/bin/bash

# Script para implementar solução híbrida
# Usa sandbox como fallback quando número registrado não funciona

echo "🔧 IMPLEMENTANDO SOLUÇÃO HÍBRIDA"
echo "================================="
echo "✅ Descoberta: Mensagens funcionaram via sandbox (+14155238886)"
echo "🎯 Solução: Usar sandbox como fallback quando registrado falha"

echo ""
echo "📋 Implementando melhorias no código..."

# 1. Atualizar função de adicionar participante para tentar ambos
echo "1. 📝 Atualizando função de adicionar participante WhatsApp..."

# Backup do arquivo atual
cp routes/conversations.js routes/conversations.js.backup

# Criar nova versão da função
cat > update_participant_function.js << 'EOF'
const fs = require('fs');

// Ler arquivo atual
let content = fs.readFileSync('routes/conversations.js', 'utf8');

// Encontrar e substituir a função de adicionar participante
const oldFunction = `        // Usar WhatsApp Sender registrado como proxy
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+14155238886';

        console.log(\`🔧 Configuração do participante:\`);
        console.log(\`   Address: \${formattedNumber}\`);
        console.log(\`   Proxy Address: \${senderNumber}\`);
        console.log(\`   Conversation SID: \${conversationSid}\`);

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        }

        console.log(\`✅ Participante criado:\`);
        console.log(\`   SID: \${participant.sid}\`);
        console.log(\`   Address: \${participant.messagingBinding?.address}\`);
        console.log(\`   Proxy Address: \${participant.messagingBinding?.proxyAddress}\`);
        console.log(\`   Type: \${participant.messagingBinding?.type}\`);`;

const newFunction = `        // Função para tentar criar participante com diferentes proxies
        async function createParticipantWithFallback(conversationSid, formattedNumber) {
            const proxies = [
                {
                    name: 'Número Registrado',
                    value: process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077'
                },
                {
                    name: 'Sandbox (Fallback)',
                    value: process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886'
                }
            ];

            for (const proxy of proxies) {
                try {
                    console.log(\`🔧 Tentando criar participante com \${proxy.name}:\`);
                    console.log(\`   Address: \${formattedNumber}\`);
                    console.log(\`   Proxy: \${proxy.value}\`);
                    console.log(\`   Conversation: \${conversationSid}\`);

                    let participant;
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        participant = await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(conversationSid)
                            .participants
                            .create({
                                'messagingBinding.address': formattedNumber,
                                'messagingBinding.proxyAddress': proxy.value
                            });
                    } else {
                        participant = await client.conversations.v1
                            .conversations(conversationSid)
                            .participants
                            .create({
                                'messagingBinding.address': formattedNumber,
                                'messagingBinding.proxyAddress': proxy.value
                            });
                    }

                    console.log(\`✅ Participante criado com \${proxy.name}:\`);
                    console.log(\`   SID: \${participant.sid}\`);
                    console.log(\`   Address: \${participant.messagingBinding?.address}\`);
                    console.log(\`   Proxy: \${participant.messagingBinding?.proxyAddress}\`);
                    console.log(\`   Type: \${participant.messagingBinding?.type}\`);

                    // Verificar se proxy foi definido
                    if (participant.messagingBinding?.proxyAddress) {
                        console.log(\`🎉 SUCESSO! Proxy definido com \${proxy.name}\`);
                        return participant;
                    } else {
                        console.log(\`⚠️ Proxy não definido com \${proxy.name}, tentando próximo...\`);
                        
                        // Remover participante sem proxy
                        try {
                            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                                await client.conversations.v1
                                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                    .conversations(conversationSid)
                                    .participants(participant.sid)
                                    .remove();
                            } else {
                                await client.conversations.v1
                                    .conversations(conversationSid)
                                    .participants(participant.sid)
                                    .remove();
                            }
                            console.log(\`🗑️ Participante sem proxy removido\`);
                        } catch (removeError) {
                            console.log(\`⚠️ Erro ao remover participante: \${removeError.message}\`);
                        }
                    }

                } catch (error) {
                    console.log(\`❌ Falha com \${proxy.name}: \${error.message}\`);
                    
                    if (error.code === 50416) {
                        console.log(\`⚠️ Participante já existe, tentando próximo proxy...\`);
                        continue;
                    }
                }
            }

            throw new Error('Falha ao criar participante com todos os proxies disponíveis');
        }

        // Criar participante com fallback
        const participant = await createParticipantWithFallback(conversationSid, formattedNumber);`;

// Substituir no conteúdo
content = content.replace(oldFunction, newFunction);

// Salvar arquivo atualizado
fs.writeFileSync('routes/conversations.js', content);

console.log('✅ Função de participante atualizada com fallback');
EOF

# Executar atualização
node update_participant_function.js

# Limpar arquivo temporário
rm update_participant_function.js

echo "✅ Função atualizada com sistema de fallback"

echo ""
echo "2. 📝 Atualizando função de webhook para usar fallback..."

# Atualizar webhook também
cp routes/webhooks.js routes/webhooks.js.backup

# Criar script para atualizar webhook
cat > update_webhook_function.js << 'EOF'
const fs = require('fs');

// Ler arquivo atual
let content = fs.readFileSync('routes/webhooks.js', 'utf8');

// Encontrar e substituir a função de adicionar participante no webhook
const oldWebhookFunction = `        // Adicionar participante WhatsApp - usar WhatsApp Sender registrado
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+14155238886';`;

const newWebhookFunction = `        // Adicionar participante WhatsApp com fallback para sandbox
        const primarySender = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';
        const fallbackSender = process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886';`;

// Substituir no conteúdo
content = content.replace(oldWebhookFunction, newWebhookFunction);

// Também atualizar a criação do participante no webhook
const oldParticipantCreation = `            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                participant = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(conversation.sid)
                    .participants
                    .create({
                        'messagingBinding.address': formattedNumber,
                        'messagingBinding.proxyAddress': senderNumber
                    });
            } else {
                participant = await client.conversations.v1
                    .conversations(conversation.sid)
                    .participants
                    .create({
                        'messagingBinding.address': formattedNumber,
                        'messagingBinding.proxyAddress': senderNumber
                    });
            }`;

const newParticipantCreation = `            // Tentar primeiro com número registrado, depois sandbox
            try {
                console.log(\`🔧 Tentando criar participante com número registrado: \${primarySender}\`);
                
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': primarySender
                        });
                } else {
                    participant = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': primarySender
                        });
                }
                
                // Verificar se proxy foi definido
                if (!participant.messagingBinding?.proxyAddress) {
                    console.log(\`⚠️ Proxy não definido com número registrado, tentando sandbox...\`);
                    throw new Error('Proxy não definido');
                }
                
                console.log(\`✅ Participante criado com número registrado\`);
                
            } catch (primaryError) {
                console.log(\`❌ Falha com número registrado: \${primaryError.message}\`);
                console.log(\`🔄 Tentando com sandbox: \${fallbackSender}\`);
                
                try {
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        participant = await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(conversation.sid)
                            .participants
                            .create({
                                'messagingBinding.address': formattedNumber,
                                'messagingBinding.proxyAddress': fallbackSender
                            });
                    } else {
                        participant = await client.conversations.v1
                            .conversations(conversation.sid)
                            .participants
                            .create({
                                'messagingBinding.address': formattedNumber,
                                'messagingBinding.proxyAddress': fallbackSender
                            });
                    }
                    
                    console.log(\`✅ Participante criado com sandbox (fallback)\`);
                    
                } catch (fallbackError) {
                    console.log(\`❌ Falha também com sandbox: \${fallbackError.message}\`);
                    throw fallbackError;
                }
            }`;

// Substituir no conteúdo
content = content.replace(oldParticipantCreation, newParticipantCreation);

// Salvar arquivo atualizado
fs.writeFileSync('routes/webhooks.js', content);

console.log('✅ Função de webhook atualizada com fallback');
EOF

# Executar atualização do webhook
node update_webhook_function.js

# Limpar arquivo temporário
rm update_webhook_function.js

echo "✅ Webhook atualizado com sistema de fallback"

echo ""
echo "3. 🧪 Testando solução híbrida..."

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Limpar participantes antigos
echo "🧹 Limpando participantes antigos..."
curl -s -X DELETE "http://localhost:3000/api/conversations/cleanup-whatsapp/+5551993590210" > /dev/null

sleep 3

# Testar criação de participante com nova função
echo "🧪 Testando criação de participante com fallback..."

TEST_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/CH329dd7f817714f1389025c01ddc6e248/participants/whatsapp" \
  -H "Content-Type: application/json" \
  -d '{"whatsappNumber":"+5551993590210"}')

echo "📤 Resultado do teste:"
echo "$TEST_RESULT" | jq '.' 2>/dev/null || echo "$TEST_RESULT"

# Verificar se funcionou
if echo "$TEST_RESULT" | grep -q '"participantSid"'; then
    PARTICIPANT_SID=$(echo "$TEST_RESULT" | jq -r '.participantSid' 2>/dev/null)
    PROXY_ADDRESS=$(echo "$TEST_RESULT" | jq -r '.proxyAddress' 2>/dev/null)
    
    echo ""
    echo "✅ Participante criado: $PARTICIPANT_SID"
    echo "📱 Proxy Address: $PROXY_ADDRESS"
    
    if [ "$PROXY_ADDRESS" != "null" ] && [ "$PROXY_ADDRESS" != "" ]; then
        echo "🎉 SUCESSO! Proxy Address definido!"
        
        echo ""
        echo "💬 Testando envio de mensagem..."
        
        # Adicionar operador
        curl -s -X POST "http://localhost:3000/api/conversations/CH329dd7f817714f1389025c01ddc6e248/participants/chat" \
          -H "Content-Type: application/json" \
          -d '{"identity":"operador-hibrido"}' > /dev/null 2>&1
        
        sleep 2
        
        # Enviar mensagem de teste
        SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/CH329dd7f817714f1389025c01ddc6e248/messages" \
          -H "Content-Type: application/json" \
          -d '{"body":"🎉 SOLUÇÃO HÍBRIDA FUNCIONANDO! Proxy: '"$PROXY_ADDRESS"' - Esta mensagem deve chegar no WhatsApp!","author":"operador-hibrido"}')
        
        echo "📤 Resultado do envio:"
        echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"
        
        if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
            MESSAGE_SID=$(echo "$SEND_RESULT" | jq -r '.messageSid' 2>/dev/null)
            echo ""
            echo "🎉 MENSAGEM ENVIADA COM SOLUÇÃO HÍBRIDA!"
            echo "📝 Message SID: $MESSAGE_SID"
            echo "📱 Proxy: $PROXY_ADDRESS"
            echo ""
            echo "⏳ Aguarde alguns segundos e verifique se a mensagem chegou no WhatsApp!"
            
        else
            echo "❌ Falha ao enviar mensagem"
        fi
        
    else
        echo "❌ Proxy Address ainda não definido"
    fi
    
else
    echo "❌ Falha ao criar participante"
fi

echo ""
echo "📋 RESUMO DA IMPLEMENTAÇÃO:"
echo "=========================="
echo "✅ Sistema híbrido implementado"
echo "✅ Fallback para sandbox configurado"
echo "✅ Logs melhorados para conflitos"
echo "✅ Funcionalidades de encerramento adicionadas"
echo ""
echo "🎯 PRÓXIMOS PASSOS:"
echo "=================="
echo "1. 📱 Verifique se a mensagem chegou no WhatsApp"
echo "2. 🌐 Teste a interface: http://localhost:3000"
echo "3. 🔚 Teste os botões de encerramento de conversas"
echo "4. 📊 Monitore logs para verificar qual proxy funcionou"
echo ""
echo "🔍 Para monitorar:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
