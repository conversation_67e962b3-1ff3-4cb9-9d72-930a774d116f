#!/bin/bash

# Script de inicialização para Mac
# Twilio WhatsApp Typing Indicator Test

echo "🚀 Iniciando Twilio WhatsApp Typing Indicator Test"
echo "=================================================="

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado!"
    echo "📥 Instale Node.js em: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js encontrado: $(node --version)"

# Verificar se npm está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não encontrado!"
    exit 1
fi

echo "✅ npm encontrado: $(npm --version)"

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
fi

# Verificar se o arquivo .env existe
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado!"
    echo "📝 Copiando .env.example para .env..."
    cp .env.example .env
    echo "🔧 Por favor, edite o arquivo .env com suas credenciais da Twilio"
    echo "📖 Consulte docs/TWILIO_SETUP.md para instruções detalhadas"
    exit 1
fi

# Verificar se as variáveis essenciais estão configuradas
source .env
if [ -z "$TWILIO_ACCOUNT_SID" ] || [ -z "$TWILIO_AUTH_TOKEN" ]; then
    echo "⚠️  Credenciais da Twilio não configuradas no arquivo .env"
    echo "🔧 Por favor, edite o arquivo .env com suas credenciais"
    echo "📖 Consulte docs/TWILIO_SETUP.md para instruções"
    exit 1
fi

echo "✅ Configuração verificada"

# Verificar se a porta está disponível
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Porta 3000 já está em uso"
    echo "🔄 Tentando parar processo existente..."
    pkill -f "node server.js" 2>/dev/null || true
    sleep 2
fi

echo "🌐 Iniciando servidor..."
echo "📱 A aplicação estará disponível em: http://localhost:3000"
echo "🛑 Para parar o servidor, pressione Ctrl+C"
echo ""

# Iniciar o servidor
npm start
