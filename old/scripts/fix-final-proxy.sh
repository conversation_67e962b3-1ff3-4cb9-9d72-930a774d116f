#!/bin/bash

# Script final para corrigir o Proxy Address
# Remove participante sem proxy e cria novo com configuração correta

echo "🔧 CORREÇÃO FINAL - Proxy Address do WhatsApp"
echo "============================================="

# Configurações
CLIENT_NUMBER="+5551993590210"
CONVERSATION_SID="CH329dd7f817714f1389025c01ddc6e248"
BROKEN_PARTICIPANT_SID="MBa7438e391f32416eb5d700a535e5fa8f"

echo "📱 Cliente: $CLIENT_NUMBER"
echo "💬 Conversa: $CONVERSATION_SID"
echo "🗑️  Participante sem proxy: $BROKEN_PARTICIPANT_SID"

echo ""
echo "🗑️  Removendo participante sem proxy..."

# Criar script Node.js para remover participante específico
cat > remove_broken_participant.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

async function removeBrokenParticipant() {
    try {
        const conversationSid = process.argv[2];
        const participantSid = process.argv[3];
        
        console.log(`🗑️  Removendo participante sem proxy: ${participantSid}`);
        
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants(participantSid)
                .remove();
        } else {
            await client.conversations.v1
                .conversations(conversationSid)
                .participants(participantSid)
                .remove();
        }

        console.log(`✅ Participante ${participantSid} removido com sucesso`);

    } catch (error) {
        console.error('❌ Erro ao remover participante:', error.message);
        process.exit(1);
    }
}

removeBrokenParticipant();
EOF

# Executar remoção
node remove_broken_participant.js "$CONVERSATION_SID" "$BROKEN_PARTICIPANT_SID"

# Limpar arquivo temporário
rm remove_broken_participant.js

if [ $? -eq 0 ]; then
    echo "✅ Participante sem proxy removido"
    
    echo ""
    echo "⏳ Aguardando 3 segundos..."
    sleep 3
    
    echo ""
    echo "➕ Criando novo participante com proxy correto..."
    
    # Criar script Node.js para adicionar participante com proxy correto
    cat > add_correct_participant.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

async function addCorrectParticipant() {
    try {
        const conversationSid = process.argv[2];
        const clientNumber = process.argv[3];
        const formattedNumber = clientNumber.startsWith('whatsapp:') ? clientNumber : `whatsapp:${clientNumber}`;
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+***********';
        
        console.log(`➕ Adicionando participante WhatsApp:`);
        console.log(`   Address: ${formattedNumber}`);
        console.log(`   Proxy Address: ${senderNumber}`);
        console.log(`   Conversation: ${conversationSid}`);
        
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        }

        console.log(`✅ Participante criado com sucesso:`);
        console.log(`   SID: ${participant.sid}`);
        console.log(`   Address: ${participant.messagingBinding?.address}`);
        console.log(`   Proxy Address: ${participant.messagingBinding?.proxyAddress}`);
        console.log(`   Type: ${participant.messagingBinding?.type}`);
        
        // Retornar dados para o script bash
        console.log(`PARTICIPANT_SID=${participant.sid}`);
        console.log(`PROXY_ADDRESS=${participant.messagingBinding?.proxyAddress || 'N/A'}`);

    } catch (error) {
        console.error('❌ Erro ao adicionar participante:', error.message);
        process.exit(1);
    }
}

addCorrectParticipant();
EOF

    # Executar adição e capturar resultado
    ADD_OUTPUT=$(node add_correct_participant.js "$CONVERSATION_SID" "$CLIENT_NUMBER")
    ADD_RESULT=$?
    
    # Limpar arquivo temporário
    rm add_correct_participant.js
    
    echo "$ADD_OUTPUT"
    
    if [ $ADD_RESULT -eq 0 ]; then
        # Extrair dados do output
        NEW_PARTICIPANT_SID=$(echo "$ADD_OUTPUT" | grep "PARTICIPANT_SID=" | cut -d'=' -f2)
        PROXY_ADDRESS=$(echo "$ADD_OUTPUT" | grep "PROXY_ADDRESS=" | cut -d'=' -f2)
        
        echo ""
        echo "🎉 SUCESSO! Novo participante criado:"
        echo "   SID: $NEW_PARTICIPANT_SID"
        echo "   Proxy: $PROXY_ADDRESS"
        
        if [[ "$PROXY_ADDRESS" == *"+18382700077"* ]]; then
            echo "✅ Proxy Address configurado corretamente!"
            
            echo ""
            echo "💬 Testando envio de mensagem final..."
            
            # Adicionar operador se necessário
            curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
              -H "Content-Type: application/json" \
              -d '{"identity":"operador-final"}' > /dev/null 2>&1
            
            sleep 2
            
            # Enviar mensagem de teste final
            SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
              -H "Content-Type: application/json" \
              -d '{"body":"🎉 TESTE FINAL CORRIGIDO! Esta mensagem deve chegar no WhatsApp com proxy correto: '"$PROXY_ADDRESS"'","author":"operador-final"}')
            
            echo "📤 Resultado do envio:"
            echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"
            
            if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
                MESSAGE_SID=$(echo "$SEND_RESULT" | jq -r '.messageSid' 2>/dev/null)
                echo ""
                echo "🎉 MENSAGEM ENVIADA COM PROXY CORRETO!"
                echo "📝 Message SID: $MESSAGE_SID"
                echo "📱 Proxy Address: $PROXY_ADDRESS"
                echo ""
                echo "⏳ Aguardando 20 segundos para verificar entrega..."
                
                # Monitorar logs por 20 segundos
                echo "📊 Monitorando logs de entrega..."
                
                for i in {1..20}; do
                    sleep 1
                    
                    # Verificar se há logs de status para esta mensagem
                    if grep -q "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                        echo ""
                        echo "📋 Status encontrado para mensagem $MESSAGE_SID:"
                        grep "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -3
                        break
                    fi
                    
                    # Verificar se há novos logs de entrega
                    RECENT_DELIVERY=$(tail -5 logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null | grep -E "(sent|delivered|read|failed)" | tail -1)
                    if [[ "$RECENT_DELIVERY" == *"$(date +%Y-%m-%d)"* ]]; then
                        TIMESTAMP=$(echo "$RECENT_DELIVERY" | jq -r '.timestamp' 2>/dev/null)
                        if [[ "$TIMESTAMP" > "$(date -u -v-30s +%Y-%m-%dT%H:%M:%S)" ]]; then
                            echo ""
                            echo "📱 Log de entrega recente encontrado:"
                            echo "$RECENT_DELIVERY" | jq '.' 2>/dev/null || echo "$RECENT_DELIVERY"
                            break
                        fi
                    fi
                    
                    echo -n "."
                done
                
                echo ""
                echo ""
                echo "📋 RESUMO FINAL DA CORREÇÃO:"
                echo "============================"
                echo "✅ Participante antigo (sem proxy) removido"
                echo "✅ Novo participante criado com proxy correto"
                echo "✅ Mensagem enviada com sucesso"
                echo "📱 Proxy Address: $PROXY_ADDRESS"
                echo "💬 Message SID: $MESSAGE_SID"
                echo ""
                echo "🎯 TESTE MANUAL AGORA:"
                echo "====================="
                echo "1. 📱 Verifique se a mensagem chegou no WhatsApp ($CLIENT_NUMBER)"
                echo "2. 📱 Responda no WhatsApp para testar o fluxo completo"
                echo "3. 🌐 Acesse http://localhost:3000"
                echo "4. 👨‍💼 Conecte como operador"
                echo "5. 💬 Responda mensagens através da interface"
                echo ""
                echo "🔍 Para monitorar logs:"
                echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
                echo ""
                
                # Verificar se ainda há erros 63015
                if grep -q "63015" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                    LAST_63015=$(grep "63015" logs/twilio-$(date +%Y-%m-%d).log | tail -1)
                    LAST_63015_TIME=$(echo "$LAST_63015" | jq -r '.timestamp' 2>/dev/null)
                    
                    if [[ "$LAST_63015_TIME" > "$(date -u -v-5M +%Y-%m-%dT%H:%M:%S)" ]]; then
                        echo "⚠️  ATENÇÃO: Ainda há erros 63015 recentes"
                        echo "   Último erro: $LAST_63015_TIME"
                        echo "   Pode ser necessária configuração adicional"
                    else
                        echo "✅ Nenhum erro 63015 recente - Problema resolvido!"
                    fi
                else
                    echo "✅ Nenhum erro 63015 encontrado - Sistema funcionando!"
                fi
                
            else
                echo "❌ Falha ao enviar mensagem de teste"
                echo "🔍 Verifique a configuração"
            fi
            
        else
            echo "❌ Proxy Address ainda incorreto: $PROXY_ADDRESS"
            echo "🔧 Configuração manual necessária"
        fi
        
    else
        echo "❌ Falha ao adicionar novo participante"
    fi
    
else
    echo "❌ Falha ao remover participante antigo"
fi

echo ""
echo "🔧 Correção final concluída!"
