#!/bin/bash

# Script de teste para o sistema reativo de chat WhatsApp
# Twilio WhatsApp Chat System

echo "🔄 Testando Sistema Reativo de Chat WhatsApp"
echo "============================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Função para executar teste
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_pattern="$3"
    
    echo -n "🔍 $test_name... "
    
    result=$(eval "$test_command" 2>&1)
    exit_code=$?
    
    if [ $exit_code -eq 0 ] && [[ "$result" =~ $expected_pattern ]]; then
        echo "✅ PASSOU"
        return 0
    else
        echo "❌ FALHOU"
        echo "   Resultado: $result"
        return 1
    fi
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

echo ""
echo "🧪 Fase 1: Testes Básicos do Sistema"
echo "===================================="

# Teste 1: Verificação de Credenciais
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Verificação de Credenciais" "curl -s http://localhost:3000/api/auth/verify" '"status":"OK"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 2: Geração de Token para Operador
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Geração de Token para Operador" "curl -s -X POST http://localhost:3000/api/auth/token -H 'Content-Type: application/json' -d '{\"identity\":\"operador-1\"}'" '"token"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 3: Interface Web Reativa
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Interface Web Reativa" "curl -s http://localhost:3000/" "Sistema de chat reativo"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

echo ""
echo "🌐 Fase 2: Testes de Webhooks"
echo "============================="

# Teste 4: Webhook de Teste
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Webhook de Teste" "curl -s http://localhost:3000/webhooks/test" "Webhooks funcionando"; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 5: Endpoint de Logs
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Endpoint de Logs" "curl -s http://localhost:3000/webhooks/logs" '"logs"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

echo ""
echo "💬 Fase 3: Testes de Conversas"
echo "=============================="

# Teste 6: Listar Conversas
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if run_test "Listar Conversas" "curl -s http://localhost:3000/api/conversations" '"conversations"'; then
    PASSED_TESTS=$((PASSED_TESTS + 1))
fi

# Teste 7: Verificar se não há funcionalidade de criação proativa
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ ! "$result" =~ "Conectar ao WhatsApp" ]] && [[ "$result" =~ "Aguardando mensagens dos usuários" ]]; then
    echo "🔍 Sistema Reativo (sem criação proativa)... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Sistema Reativo (sem criação proativa)... ❌ FALHOU"
fi

echo ""
echo "🎯 Fase 4: Verificações de Interface"
echo "===================================="

# Teste 8: Verificar elementos da interface reativa
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "Status do Sistema" ]] && [[ "$result" =~ "Conversas Ativas" ]] && [[ "$result" =~ "Conectar como Operador" ]]; then
    echo "🔍 Elementos da Interface Reativa... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Elementos da Interface Reativa... ❌ FALHOU"
fi

# Teste 9: Verificar instruções do sistema reativo
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "Sistema Reativo" ]] && [[ "$result" =~ "Para Operadores" ]]; then
    echo "🔍 Instruções do Sistema Reativo... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Instruções do Sistema Reativo... ❌ FALHOU"
fi

# Teste 10: Verificar configuração WhatsApp Sender
TOTAL_TESTS=$((TOTAL_TESTS + 1))
result=$(curl -s http://localhost:3000/)
if [[ "$result" =~ "+18382700077" ]] && [[ "$result" =~ "registrado" ]]; then
    echo "🔍 Configuração WhatsApp Sender... ✅ PASSOU"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo "🔍 Configuração WhatsApp Sender... ❌ FALHOU"
fi

# Resultado final
echo ""
echo "📊 Resultado dos Testes do Sistema Reativo"
echo "=========================================="
echo "✅ Testes Passaram: $PASSED_TESTS"
echo "❌ Testes Falharam: $((TOTAL_TESTS - PASSED_TESTS))"
echo "📈 Total de Testes: $TOTAL_TESTS"
echo "📊 Taxa de Sucesso: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo ""
    echo "🎉 TODOS OS TESTES DO SISTEMA REATIVO PASSARAM!"
    echo "🔄 O sistema agora funciona de forma reativa:"
    echo "   ✅ Aguarda mensagens dos usuários finais"
    echo "   ✅ Cria conversas automaticamente quando necessário"
    echo "   ✅ Interface focada em operadores"
    echo "   ✅ Monitoramento de conversas ativas"
    echo "   ✅ Sistema totalmente automático"
    echo ""
    echo "🚀 Próximos passos:"
    echo "   1. Acesse: http://localhost:3000"
    echo "   2. Clique em 'Conectar como Operador'"
    echo "   3. Configure os webhooks na Twilio Console"
    echo "   4. Envie uma mensagem WhatsApp para +18382700077"
    echo "   5. Veja a conversa aparecer automaticamente na interface"
    echo "   6. Responda através da interface web"
    exit 0
else
    echo ""
    echo "⚠️  Alguns testes falharam. Verifique a implementação."
    exit 1
fi
