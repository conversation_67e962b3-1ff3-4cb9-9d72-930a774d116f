#!/bin/bash

# Script para testar entrega de mensagens WhatsApp
# Verifica se o participante está configurado corretamente

echo "🧪 Testando Entrega de Mensagens WhatsApp"
echo "========================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Configurações
CLIENT_NUMBER="+5551993590210"
FORMATTED_NUMBER="whatsapp:$CLIENT_NUMBER"

echo "📱 Número do cliente: $CLIENT_NUMBER"
echo "📱 Número formatado: $FORMATTED_NUMBER"

echo ""
echo "🔍 Verificando conversas ativas..."

# Obter lista de conversas
CONVERSATIONS=$(curl -s http://localhost:3000/api/conversations)
CONVERSATION_SID=$(echo "$CONVERSATIONS" | jq -r '.conversations[0].sid' 2>/dev/null)

if [ "$CONVERSATION_SID" = "null" ] || [ -z "$CONVERSATION_SID" ]; then
    echo "❌ Nenhuma conversa encontrada"
    echo "📝 Criando nova conversa..."
    
    # Simular webhook para criar conversa
    curl -s -X POST http://localhost:3000/webhooks/whatsapp/incoming \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "From=$FORMATTED_NUMBER" \
      -d "To=whatsapp:+***********" \
      -d "Body=Teste de entrega" \
      -d "MessageSid=TEST_$(date +%s)" \
      -d "AccountSid=TEST" \
      -d "ApiVersion=2010-04-01" > /dev/null
    
    echo "⏳ Aguardando criação da conversa..."
    sleep 3
    
    # Obter conversa novamente
    CONVERSATIONS=$(curl -s http://localhost:3000/api/conversations)
    CONVERSATION_SID=$(echo "$CONVERSATIONS" | jq -r '.conversations[0].sid' 2>/dev/null)
fi

if [ "$CONVERSATION_SID" = "null" ] || [ -z "$CONVERSATION_SID" ]; then
    echo "❌ Falha ao criar/encontrar conversa"
    exit 1
fi

echo "✅ Usando conversa: $CONVERSATION_SID"

echo ""
echo "🔍 Verificando participantes da conversa..."

# Verificar participantes via API Twilio diretamente
echo "📋 Consultando participantes via Twilio API..."

# Usar curl para consultar participantes (simulando o que o sistema faz)
echo "🔄 Adicionando participante WhatsApp com número registrado..."

# Adicionar participante WhatsApp
ADD_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/whatsapp" \
  -H "Content-Type: application/json" \
  -d "{\"whatsappNumber\":\"$CLIENT_NUMBER\"}")

echo "📤 Resultado da adição:"
echo "$ADD_RESULT" | jq '.' 2>/dev/null || echo "$ADD_RESULT"

# Verificar se foi adicionado com sucesso
if echo "$ADD_RESULT" | grep -q "participantSid\|already exists"; then
    echo "✅ Participante WhatsApp configurado"
    
    # Extrair proxy address para verificar
    PROXY_ADDRESS=$(echo "$ADD_RESULT" | jq -r '.proxyAddress' 2>/dev/null)
    if [ "$PROXY_ADDRESS" != "null" ] && [ ! -z "$PROXY_ADDRESS" ]; then
        echo "📱 Proxy Address: $PROXY_ADDRESS"
        
        if [[ "$PROXY_ADDRESS" == *"+***********"* ]]; then
            echo "✅ Usando WhatsApp Sender registrado correto!"
        else
            echo "⚠️  Usando WhatsApp Sender: $PROXY_ADDRESS"
        fi
    fi
else
    echo "❌ Falha ao adicionar participante WhatsApp"
fi

echo ""
echo "👤 Adicionando operador à conversa..."

# Adicionar operador
curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
  -H "Content-Type: application/json" \
  -d '{"identity":"operador-teste"}' > /dev/null

echo "⏳ Aguardando 2 segundos..."
sleep 2

echo ""
echo "💬 Enviando mensagem de teste..."

# Enviar mensagem
SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
  -H "Content-Type: application/json" \
  -d '{"body":"Teste de entrega - sistema corrigido","author":"operador-teste"}')

echo "📤 Resultado do envio:"
echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"

if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
    MESSAGE_SID=$(echo "$SEND_RESULT" | jq -r '.messageSid' 2>/dev/null)
    echo "✅ Mensagem enviada com sucesso!"
    echo "📝 Message SID: $MESSAGE_SID"
    
    echo ""
    echo "⏳ Aguardando 10 segundos para verificar entrega..."
    sleep 10
    
    echo ""
    echo "📊 Verificando logs de entrega..."
    
    # Verificar logs recentes
    echo "📝 Logs recentes (últimas 10 linhas):"
    tail -10 logs/twilio-$(date +%Y-%m-%d).log | grep -E "(delivery|failed|sent|delivered)" || echo "Nenhum log de entrega encontrado"
    
    echo ""
    echo "🔍 Verificando status da mensagem nos logs..."
    
    # Procurar por logs relacionados à mensagem
    if grep -q "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
        echo "📋 Logs da mensagem $MESSAGE_SID:"
        grep "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -5
    else
        echo "📭 Nenhum log específico da mensagem encontrado ainda"
    fi
    
    echo ""
    echo "🔍 Verificando logs de erro 63015..."
    
    # Verificar se ainda há erros 63015
    if grep -q "63015" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
        RECENT_63015=$(grep "63015" logs/twilio-$(date +%Y-%m-%d).log | tail -1)
        echo "⚠️  Último erro 63015 encontrado:"
        echo "$RECENT_63015"
        
        # Verificar se é recente (últimos 5 minutos)
        RECENT_TIME=$(echo "$RECENT_63015" | grep -o '"timestamp":"[^"]*"' | cut -d'"' -f4)
        echo "🕐 Timestamp do erro: $RECENT_TIME"
        
        if [[ "$RECENT_TIME" > "$(date -u -v-5M +%Y-%m-%dT%H:%M:%S)" ]]; then
            echo "❌ Erro 63015 ainda está ocorrendo (últimos 5 minutos)"
            echo "🔧 O problema ainda não foi completamente resolvido"
        else
            echo "✅ Erro 63015 é antigo, problema pode estar resolvido"
        fi
    else
        echo "✅ Nenhum erro 63015 encontrado nos logs"
    fi
    
else
    echo "❌ Falha ao enviar mensagem"
    echo "🔍 Verifique a configuração da conversa"
fi

echo ""
echo "📋 Resumo do Teste:"
echo "=================="
echo "📱 Cliente: $CLIENT_NUMBER"
echo "💬 Conversa: $CONVERSATION_SID"
echo "🔧 WhatsApp Sender: +*********** (registrado)"
echo ""
echo "🎯 Próximos passos:"
echo "1. 📱 Envie uma mensagem real do WhatsApp para +***********"
echo "2. 🌐 Acesse http://localhost:3000 e conecte como operador"
echo "3. 💬 Responda através da interface web"
echo "4. 📱 Verifique se a resposta chega no WhatsApp"
echo ""
echo "🔍 Para monitorar em tempo real:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
