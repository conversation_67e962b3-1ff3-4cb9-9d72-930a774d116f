#!/bin/bash

# Script para configurar ngrok e expor webhooks publicamente
# Twilio WhatsApp Typing Indicator Test

echo "🌐 Configurando Túnel Público com ngrok"
echo "======================================="

# Verificar se ngrok está instalado
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok não encontrado!"
    echo ""
    echo "📥 Para instalar ngrok:"
    echo "1. Acesse: https://ngrok.com/"
    echo "2. Crie uma conta gratuita"
    echo "3. Baixe ngrok para macOS"
    echo "4. Instale com: brew install ngrok/ngrok/ngrok"
    echo "5. Configure com: ngrok config add-authtoken SEU_TOKEN"
    echo ""
    exit 1
fi

echo "✅ ngrok encontrado: $(ngrok version)"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando na porta 3000"
    echo "🚀 Inicie o servidor primeiro com: node server.js"
    exit 1
fi

echo "✅ Servidor está rodando na porta 3000"

# Verificar se já existe um túnel ngrok ativo
if pgrep -f "ngrok.*3000" > /dev/null; then
    echo "⚠️  Túnel ngrok já está ativo"
    echo "🔍 Verificando URL atual..."
    
    # Tentar obter a URL do ngrok
    sleep 2
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null)
    
    if [ "$NGROK_URL" != "null" ] && [ ! -z "$NGROK_URL" ]; then
        echo "🌐 URL pública ativa: $NGROK_URL"
    else
        echo "❌ Não foi possível obter a URL do ngrok"
        echo "🔄 Reiniciando túnel..."
        pkill -f "ngrok.*3000"
        sleep 2
    fi
fi

# Iniciar ngrok se não estiver rodando
if ! pgrep -f "ngrok.*3000" > /dev/null; then
    echo "🚀 Iniciando túnel ngrok..."
    
    # Iniciar ngrok em background
    ngrok http 3000 --log=stdout > ngrok.log 2>&1 &
    NGROK_PID=$!
    
    echo "⏳ Aguardando ngrok inicializar..."
    sleep 5
    
    # Verificar se ngrok iniciou corretamente
    if ! kill -0 $NGROK_PID 2>/dev/null; then
        echo "❌ Falha ao iniciar ngrok"
        cat ngrok.log
        exit 1
    fi
    
    # Obter URL pública
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null)
    
    if [ "$NGROK_URL" = "null" ] || [ -z "$NGROK_URL" ]; then
        echo "❌ Não foi possível obter URL do ngrok"
        echo "📋 Log do ngrok:"
        cat ngrok.log
        exit 1
    fi
fi

echo "✅ Túnel ngrok ativo!"
echo "🌐 URL pública: $NGROK_URL"

# Gerar URLs dos webhooks
WEBHOOK_INCOMING="$NGROK_URL/webhooks/whatsapp/incoming"
WEBHOOK_STATUS="$NGROK_URL/webhooks/whatsapp/status"
WEBHOOK_CONVERSATIONS="$NGROK_URL/webhooks/conversations"

echo ""
echo "📋 URLs dos Webhooks para configurar na Twilio:"
echo "=============================================="
echo ""
echo "🔗 WhatsApp Sandbox - When a message comes in:"
echo "   $WEBHOOK_INCOMING"
echo ""
echo "🔗 WhatsApp Sandbox - Status callback URL:"
echo "   $WEBHOOK_STATUS"
echo ""
echo "🔗 Conversations Service - Webhook URL:"
echo "   $WEBHOOK_CONVERSATIONS"
echo ""

# Testar webhooks
echo "🧪 Testando endpoints dos webhooks..."
echo ""

# Teste webhook test
TEST_RESULT=$(curl -s "$NGROK_URL/webhooks/test")
if [[ "$TEST_RESULT" =~ "Webhooks funcionando" ]]; then
    echo "✅ Webhook de teste: OK"
else
    echo "❌ Webhook de teste: FALHOU"
fi

# Salvar URLs em arquivo para referência
cat > webhook-urls.txt << EOF
# URLs dos Webhooks - Gerado em $(date)
# Configure estas URLs no Twilio Console

## WhatsApp Sandbox
When a message comes in: $WEBHOOK_INCOMING
Status callback URL: $WEBHOOK_STATUS

## Conversations Service  
Webhook URL: $WEBHOOK_CONVERSATIONS

## URLs para teste
Público: $NGROK_URL
Teste: $NGROK_URL/webhooks/test
Logs: $NGROK_URL/webhooks/logs

## Como configurar:
1. Acesse: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
2. Configure "When a message comes in" com: $WEBHOOK_INCOMING
3. Configure "Status callback URL" com: $WEBHOOK_STATUS
4. Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
5. Selecione seu service e configure Webhook URL: $WEBHOOK_CONVERSATIONS
EOF

echo "📄 URLs salvas em: webhook-urls.txt"
echo ""
echo "🔧 Próximos passos:"
echo "=================="
echo "1. 📋 Copie as URLs acima"
echo "2. 🌐 Acesse o Twilio Console"
echo "3. ⚙️  Configure os webhooks conforme indicado"
echo "4. 🧪 Teste enviando mensagem do WhatsApp"
echo ""
echo "💡 Para parar o túnel: pkill -f 'ngrok.*3000'"
echo "📊 Para ver logs: tail -f ngrok.log"
echo "🔍 Para ver logs da aplicação: curl $NGROK_URL/webhooks/logs"

# Manter o script rodando para mostrar logs em tempo real
echo ""
echo "📡 Monitorando webhooks em tempo real..."
echo "🛑 Pressione Ctrl+C para parar"
echo "=================================="

# Função para mostrar logs em tempo real
show_realtime_logs() {
    while true; do
        sleep 5
        RECENT_LOGS=$(curl -s "$NGROK_URL/webhooks/logs?hours=1" | jq -r '.logs[] | "\(.timestamp) [\(.type)] \(.body.Body // .body.EventType // .message // "N/A")"' 2>/dev/null | head -5)
        
        if [ ! -z "$RECENT_LOGS" ]; then
            clear
            echo "📡 Webhooks Ativos - Logs Recentes:"
            echo "=================================="
            echo "🌐 URL: $NGROK_URL"
            echo ""
            echo "$RECENT_LOGS"
            echo ""
            echo "🔄 Atualizando a cada 5 segundos..."
            echo "🛑 Pressione Ctrl+C para parar"
        fi
    done
}

# Capturar Ctrl+C para limpeza
trap 'echo ""; echo "🛑 Parando monitoramento..."; echo "💡 Túnel ngrok ainda está ativo em: $NGROK_URL"; exit 0' INT

# Iniciar monitoramento
show_realtime_logs
