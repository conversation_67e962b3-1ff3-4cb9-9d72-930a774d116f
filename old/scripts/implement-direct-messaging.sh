#!/bin/bash

# Implementar sistema de mensagens diretas como alternativa
# Usar API de Messaging diretamente quando Conversations falha

echo "🔧 IMPLEMENTANDO SOLUÇÃO ALTERNATIVA"
echo "===================================="
echo "Sistema de mensagens diretas para contornar problema do proxyAddress"

echo ""
echo "📝 Criando endpoint de mensagem direta..."

# Criar novo endpoint para mensagens diretas
cat > add_direct_messaging.js << 'EOF'
const fs = require('fs');

// Ler arquivo atual
let content = fs.readFileSync('routes/conversations.js', 'utf8');

// Adicionar novo endpoint antes do module.exports
const newEndpoint = `
/**
 * Enviar mensagem direta via WhatsApp (alternativa quando Conversations falha)
 * POST /api/conversations/send-direct-whatsapp
 */
router.post('/send-direct-whatsapp', async (req, res) => {
    try {
        const { to, body, from } = req.body;

        if (!to || !body) {
            return res.status(400).json({
                error: 'Destinatário (to) e mensagem (body) são obrigatórios'
            });
        }

        // Formatar números
        const toNumber = to.startsWith('whatsapp:') ? to : \`whatsapp:\${to}\`;
        const fromNumber = from || process.env.TWILIO_WHATSAPP_SENDER_NUMBER || process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886';

        console.log(\`📱 Enviando mensagem direta WhatsApp:\`);
        console.log(\`   De: \${fromNumber}\`);
        console.log(\`   Para: \${toNumber}\`);
        console.log(\`   Mensagem: \${body}\`);

        // Tentar primeiro com número registrado, depois sandbox
        const senders = [
            {
                name: 'Número Registrado',
                number: process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077'
            },
            {
                name: 'Sandbox',
                number: process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886'
            }
        ];

        let lastError = null;
        
        for (const sender of senders) {
            try {
                console.log(\`🔧 Tentando enviar com \${sender.name}: \${sender.number}\`);
                
                const message = await client.messages.create({
                    body: body,
                    from: sender.number,
                    to: toNumber
                });

                console.log(\`✅ Mensagem enviada com \${sender.name}!\`);
                console.log(\`   Message SID: \${message.sid}\`);
                console.log(\`   Status: \${message.status}\`);

                // Log da mensagem enviada
                logger.messageSent({
                    messageSid: message.sid,
                    from: sender.number,
                    to: toNumber,
                    body: body,
                    method: 'direct_whatsapp'
                });

                res.json({
                    success: true,
                    messageSid: message.sid,
                    from: sender.number,
                    to: toNumber,
                    body: body,
                    status: message.status,
                    method: 'direct_whatsapp',
                    sender: sender.name
                });

                return; // Sucesso, sair do loop

            } catch (error) {
                console.log(\`❌ Falha com \${sender.name}: \${error.message}\`);
                lastError = error;
                
                // Se é erro de número não verificado no sandbox, tentar próximo
                if (error.code === 63007 || error.message.includes('not a valid')) {
                    continue;
                }
            }
        }

        // Se chegou aqui, todos os senders falharam
        throw lastError || new Error('Falha ao enviar com todos os senders');

    } catch (error) {
        console.error('Erro ao enviar mensagem direta WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao enviar mensagem direta WhatsApp',
            details: error.message,
            code: error.code
        });
    }
});

/**
 * Testar conectividade WhatsApp
 * GET /api/conversations/test-whatsapp
 */
router.get('/test-whatsapp', async (req, res) => {
    try {
        const testNumber = req.query.number || '+5551993590210';
        const formattedNumber = testNumber.startsWith('whatsapp:') ? testNumber : \`whatsapp:\${testNumber}\`;
        
        console.log(\`🧪 Testando conectividade WhatsApp para: \${formattedNumber}\`);

        // Testar com ambos os senders
        const senders = [
            {
                name: 'Número Registrado',
                number: process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077'
            },
            {
                name: 'Sandbox',
                number: process.env.TWILIO_WHATSAPP_SANDBOX_NUMBER || 'whatsapp:+14155238886'
            }
        ];

        const results = [];

        for (const sender of senders) {
            try {
                const message = await client.messages.create({
                    body: \`🧪 Teste de conectividade WhatsApp via \${sender.name} - \${new Date().toLocaleString()}\`,
                    from: sender.number,
                    to: formattedNumber
                });

                results.push({
                    sender: sender.name,
                    number: sender.number,
                    success: true,
                    messageSid: message.sid,
                    status: message.status
                });

                console.log(\`✅ Teste com \${sender.name} bem-sucedido: \${message.sid}\`);

            } catch (error) {
                results.push({
                    sender: sender.name,
                    number: sender.number,
                    success: false,
                    error: error.message,
                    code: error.code
                });

                console.log(\`❌ Teste com \${sender.name} falhou: \${error.message}\`);
            }
        }

        const successCount = results.filter(r => r.success).length;

        res.json({
            testNumber: formattedNumber,
            results: results,
            summary: {
                total: senders.length,
                successful: successCount,
                failed: senders.length - successCount
            },
            recommendation: successCount > 0 ? 'Sistema funcionando' : 'Verificar configuração'
        });

    } catch (error) {
        console.error('Erro no teste WhatsApp:', error);
        res.status(500).json({
            error: 'Erro no teste WhatsApp',
            details: error.message
        });
    }
});

`;

// Adicionar antes do module.exports
const moduleExportsIndex = content.lastIndexOf('module.exports = router;');
if (moduleExportsIndex !== -1) {
    content = content.slice(0, moduleExportsIndex) + newEndpoint + '\n' + content.slice(moduleExportsIndex);
} else {
    content += newEndpoint + '\nmodule.exports = router;\n';
}

// Salvar arquivo atualizado
fs.writeFileSync('routes/conversations.js', content);

console.log('✅ Endpoints de mensagem direta adicionados');
EOF

# Executar adição
node add_direct_messaging.js

# Limpar arquivo temporário
rm add_direct_messaging.js

echo "✅ Endpoints de mensagem direta implementados"

echo ""
echo "📝 Atualizando interface para usar mensagem direta..."

# Adicionar botão de teste na interface
cat > update_interface.js << 'EOF'
const fs = require('fs');

// Ler arquivo HTML
let htmlContent = fs.readFileSync('public/index.html', 'utf8');

// Adicionar botão de teste WhatsApp
const testButton = `
                <button id="testWhatsAppBtn" class="btn btn-success">📱 Testar WhatsApp</button>`;

// Inserir após o botão de encerrar todas
htmlContent = htmlContent.replace(
    '<button id="closeAllConversationsBtn" class="btn btn-danger">🔚 Encerrar Todas</button>',
    '<button id="closeAllConversationsBtn" class="btn btn-danger">🔚 Encerrar Todas</button>' + testButton
);

// Salvar HTML atualizado
fs.writeFileSync('public/index.html', htmlContent);

// Ler arquivo JavaScript
let jsContent = fs.readFileSync('public/script.js', 'utf8');

// Adicionar referência ao botão
jsContent = jsContent.replace(
    'closeAllConversationsBtn: document.getElementById(\'closeAllConversationsBtn\'),',
    'closeAllConversationsBtn: document.getElementById(\'closeAllConversationsBtn\'),\n    testWhatsAppBtn: document.getElementById(\'testWhatsAppBtn\'),'
);

// Adicionar event listener
jsContent = jsContent.replace(
    'elements.closeAllConversationsBtn.addEventListener(\'click\', closeAllConversations);',
    'elements.closeAllConversationsBtn.addEventListener(\'click\', closeAllConversations);\n    elements.testWhatsAppBtn.addEventListener(\'click\', testWhatsApp);'
);

// Adicionar função de teste
const testFunction = `
// Testar WhatsApp diretamente
async function testWhatsApp() {
    const number = prompt('Digite o número para teste (ex: +5551993590210):', '+5551993590210');
    if (!number) return;

    try {
        elements.testWhatsAppBtn.disabled = true;
        elements.testWhatsAppBtn.textContent = '⏳ Testando...';
        
        addLog(\`Testando WhatsApp para: \${number}\`, 'info');
        
        const response = await fetch(\`/api/conversations/test-whatsapp?number=\${encodeURIComponent(number)}\`);
        
        if (!response.ok) {
            throw new Error(\`Erro HTTP: \${response.status}\`);
        }

        const data = await response.json();
        
        addLog(\`✅ Teste concluído: \${data.summary.successful}/\${data.summary.total} senders funcionando\`, 'success');
        
        data.results.forEach(result => {
            if (result.success) {
                addLog(\`✅ \${result.sender}: Mensagem enviada (\${result.messageSid})\`, 'success');
            } else {
                addLog(\`❌ \${result.sender}: \${result.error}\`, 'error');
            }
        });
        
        if (data.summary.successful > 0) {
            addLog(\`🎉 Sistema WhatsApp funcionando! Verifique seu telefone.\`, 'success');
        } else {
            addLog(\`⚠️ Nenhum sender funcionou. Verifique configuração.\`, 'warning');
        }

    } catch (error) {
        console.error('Erro no teste WhatsApp:', error);
        addLog(\`Erro no teste WhatsApp: \${error.message}\`, 'error');
    } finally {
        elements.testWhatsAppBtn.disabled = false;
        elements.testWhatsAppBtn.textContent = '📱 Testar WhatsApp';
    }
}`;

// Adicionar função antes do final
jsContent = jsContent.replace(
    '};',
    '};\n' + testFunction
);

// Salvar JavaScript atualizado
fs.writeFileSync('public/script.js', jsContent);

console.log('✅ Interface atualizada com teste WhatsApp');
EOF

# Executar atualização da interface
node update_interface.js

# Limpar arquivo temporário
rm update_interface.js

echo "✅ Interface atualizada"

echo ""
echo "🔄 Reiniciando servidor com mensagem direta..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
sleep 2

cd /Users/<USER>/Documents/augment-projects/twiliowhats
npm start > server.log 2>&1 &
sleep 5

if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Falha ao reiniciar servidor"
    exit 1
fi

echo "✅ Servidor reiniciado"

echo ""
echo "🧪 Testando mensagem direta..."

# Testar endpoint de mensagem direta
TEST_DIRECT=$(curl -s -X POST "http://localhost:3000/api/conversations/send-direct-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "+5551993590210",
    "body": "🎉 MENSAGEM DIRETA FUNCIONANDO! Sistema alternativo implementado com sucesso!"
  }')

echo "📤 Resultado da mensagem direta:"
echo "$TEST_DIRECT" | jq '.' 2>/dev/null || echo "$TEST_DIRECT"

if echo "$TEST_DIRECT" | grep -q '"success":true'; then
    MSG_SID=$(echo "$TEST_DIRECT" | jq -r '.messageSid' 2>/dev/null)
    SENDER=$(echo "$TEST_DIRECT" | jq -r '.sender' 2>/dev/null)
    FROM_NUMBER=$(echo "$TEST_DIRECT" | jq -r '.from' 2>/dev/null)
    
    echo ""
    echo "🎉 MENSAGEM DIRETA ENVIADA COM SUCESSO!"
    echo "📝 Message SID: $MSG_SID"
    echo "📱 Sender: $SENDER"
    echo "📞 From: $FROM_NUMBER"
    echo ""
    echo "⏳ Aguarde alguns segundos e verifique se chegou no WhatsApp!"
    
else
    echo "❌ Falha na mensagem direta"
    echo "🔍 Verificando logs..."
    tail -5 server.log
fi

echo ""
echo "🧪 Testando endpoint de teste..."

# Testar endpoint de teste
TEST_ENDPOINT=$(curl -s "http://localhost:3000/api/conversations/test-whatsapp?number=+5551993590210")

echo "📤 Resultado do teste:"
echo "$TEST_ENDPOINT" | jq '.' 2>/dev/null || echo "$TEST_ENDPOINT"

echo ""
echo "📋 SOLUÇÃO ALTERNATIVA IMPLEMENTADA"
echo "==================================="

echo "✅ Funcionalidades adicionadas:"
echo "   • Mensagem direta WhatsApp (contorna problema do Conversations)"
echo "   • Teste de conectividade WhatsApp"
echo "   • Sistema de fallback (registrado → sandbox)"
echo "   • Interface atualizada com botão de teste"

echo ""
echo "🎯 COMO USAR:"
echo "============"
echo "1. 🌐 Acesse: http://localhost:3000"
echo "2. 📱 Clique no botão 'Testar WhatsApp'"
echo "3. 📞 Digite seu número para teste"
echo "4. ✅ Verifique se a mensagem chega"

echo ""
echo "📡 ENDPOINTS DISPONÍVEIS:"
echo "========================"
echo "• POST /api/conversations/send-direct-whatsapp"
echo "• GET /api/conversations/test-whatsapp?number=+5551993590210"

echo ""
echo "🎉 SISTEMA ALTERNATIVO PRONTO!"
echo "Agora você pode enviar mensagens WhatsApp mesmo com o problema do proxyAddress!"
