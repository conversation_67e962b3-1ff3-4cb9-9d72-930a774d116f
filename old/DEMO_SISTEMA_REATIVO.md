# 🎬 Demo do Sistema Reativo - Passo a Passo

## 🎯 Demonstração Completa

Este guia mostra como usar o novo sistema reativo de chat WhatsApp.

## 🚀 Passo 1: Iniciar o Sistema

```bash
# Iniciar sistema completo com ngrok
./setup-reactive-ngrok.sh
```

**O que acontece:**
- ✅ Servidor Node.js inicia na porta 3000
- ✅ Túnel ngrok é criado
- ✅ URLs dos webhooks são geradas
- ✅ Instruções de configuração são exibidas

**Resultado esperado:**
```
🌐 Túnel ngrok ativo: https://abc123.ngrok.io
📱 WhatsApp Sender Webhooks:
   Incoming: https://abc123.ngrok.io/webhooks/whatsapp/incoming
   Status:   https://abc123.ngrok.io/webhooks/whatsapp/status
💬 Conversations Webhook:
   Events:   https://abc123.ngrok.io/webhooks/conversations
```

## 🔧 Passo 2: Configurar Webhooks na Twilio

### 2.1 WhatsApp Sender
1. Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
2. Encontre seu WhatsApp Sender: **+18382700077**
3. Configure:
   - **Webhook URL**: `https://abc123.ngrok.io/webhooks/whatsapp/incoming`
   - **Status callback URL**: `https://abc123.ngrok.io/webhooks/whatsapp/status`
   - **HTTP Method**: POST

### 2.2 Conversations Service
1. Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
2. Selecione seu Conversations Service
3. Configure:
   - **Webhook URL**: `https://abc123.ngrok.io/webhooks/conversations`
   - **HTTP Method**: POST
   - **Events**: ✅ onMessageAdded, ✅ onTypingStarted, ✅ onTypingEnded

## 👨‍💼 Passo 3: Interface do Operador

### 3.1 Acessar Interface
1. Abra: http://localhost:3000
2. Veja a nova interface reativa

### 3.2 Conectar como Operador
1. **Identidade**: Deixe "operador-1" ou mude para seu nome
2. Clique: **"Conectar como Operador"**
3. **Status esperado**: 
   - 🔗 Conexão Twilio: ✅ Operador: operador-1
   - 📱 WhatsApp Sender: ✅ +18382700077 Ativo
   - 🌐 Webhooks: ✅ Webhooks Ativos

### 3.3 Verificar Sistema
- **Conversas Ativas**: "0 conversa(s) ativa(s)"
- **Chat**: "Selecione uma conversa para responder..."
- **Logs**: Sistema iniciado e aguardando

## 📱 Passo 4: Testar o Sistema Reativo

### 4.1 Enviar Mensagem WhatsApp
**Do seu celular:**
1. Abra WhatsApp
2. Envie mensagem para: **+18382700077**
3. Texto exemplo: "Olá, preciso de ajuda!"

### 4.2 Observar Criação Automática
**Na interface web:**
1. **Logs mostrarão**:
   ```
   [HH:MM:SS] 📱 Mensagem WhatsApp recebida
   [HH:MM:SS] 🔄 Criando conversa automática
   [HH:MM:SS] 📝 Nova conversa criada
   [HH:MM:SS] 👤 Adicionando participante WhatsApp
   ```

2. **Lista de conversas atualizará**:
   - "1 conversa(s) ativa(s)"
   - Nova conversa aparecerá na lista

### 4.3 Responder como Operador
1. **Clique** na conversa na lista
2. **Chat abrirá** mostrando:
   - Mensagem recebida do usuário
   - Campo para responder
3. **Digite resposta**: "Olá! Como posso ajudar?"
4. **Clique "Enviar"**

### 4.4 Verificar Recebimento
**No seu WhatsApp:**
- Mensagem de resposta deve chegar
- Typing indicator deve funcionar quando você digitar na web

## 🧪 Passo 5: Testar Typing Indicator

### 5.1 Do WhatsApp para Web
1. **No WhatsApp**: Comece a digitar (não envie)
2. **Na interface web**: Deve aparecer "Usuário está digitando..."

### 5.2 Da Web para WhatsApp
1. **Na interface web**: Comece a digitar no campo
2. **No WhatsApp**: Deve aparecer indicador de digitação

## 📊 Passo 6: Monitorar Sistema

### 6.1 Logs em Tempo Real
**Na interface web:**
- Seção "Logs do Sistema" mostra eventos
- Auto-refresh pode ser ativado

**No terminal:**
```bash
# Logs do sistema
tail -f logs/twilio-$(date +%Y-%m-%d).log

# Logs do ngrok
tail -f ngrok.log
```

### 6.2 Verificar Webhooks
```bash
# Testar webhooks
curl https://abc123.ngrok.io/webhooks/test

# Ver logs recentes
curl https://abc123.ngrok.io/webhooks/logs
```

## 🎯 Passo 7: Testar Múltiplas Conversas

### 7.1 Segunda Conversa
1. **Use outro número** ou peça para alguém enviar mensagem
2. **Observe** nova conversa aparecer na lista
3. **Alterne** entre conversas clicando na lista

### 7.2 Gerenciar Conversas
- **Abrir**: Clique na conversa
- **Fechar**: Clique "✕ Fechar" no cabeçalho do chat
- **Atualizar**: Clique "🔄 Atualizar" na lista

## ✅ Resultados Esperados

### Interface Funcionando:
- ✅ Status do sistema todo verde
- ✅ Conversas aparecem automaticamente
- ✅ Mensagens são recebidas e enviadas
- ✅ Typing indicator funciona bidirecionalmente
- ✅ Logs mostram todos os eventos

### Fluxo Completo:
```
Usuário WhatsApp → Envia mensagem
     ↓
Twilio Platform → Recebe mensagem
     ↓
Webhook → /webhooks/whatsapp/incoming
     ↓
Sistema → Cria conversa automaticamente
     ↓
Interface → Mostra nova conversa
     ↓
Operador → Responde via web
     ↓
Usuário → Recebe resposta no WhatsApp
```

## 🐛 Troubleshooting

### Problema: Mensagem não chega na interface
**Verificar:**
1. ✅ ngrok ativo: `curl https://abc123.ngrok.io/webhooks/test`
2. ✅ Webhooks configurados na Twilio
3. ✅ Logs mostram recebimento: `tail -f logs/twilio-*.log`

### Problema: Não consegue responder
**Verificar:**
1. ✅ Operador conectado
2. ✅ Conversa selecionada
3. ✅ Campo de mensagem habilitado

### Problema: Typing indicator não funciona
**Verificar:**
1. ✅ Conversations webhook configurado
2. ✅ Eventos onTypingStarted/Ended marcados
3. ✅ Ambos participantes na mesma conversa

## 🎉 Demonstração Concluída!

O sistema reativo está funcionando perfeitamente:
- 🔄 **Reativo**: Aguarda mensagens dos usuários
- 🤖 **Automático**: Cria conversas sem intervenção
- 👨‍💼 **Operador**: Interface focada em responder
- 📱 **WhatsApp**: Integração completa e bidirecional
- 🌐 **Webhooks**: Notificações em tempo real

**Sistema pronto para produção!** 🚀
