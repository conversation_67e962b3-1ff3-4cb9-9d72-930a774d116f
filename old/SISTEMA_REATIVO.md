# 🔄 Sistema Reativo - Documentação Completa

## 📋 Visão Geral

O sistema foi completamente refatorado para funcionar de forma **reativa**, onde:

- ✅ **Aguarda mensagens** dos usuários finais
- ✅ **Cria conversas automaticamente** quando recebe primeira mensagem
- ✅ **Interface de operador** para responder mensagens
- ✅ **Webhooks configurados** para receber notificações em tempo real
- ✅ **Sem necessidade de templates** ou configuração prévia

## 🔄 Diferenças do Sistema Anterior

| Aspecto | Sistema Anterior | Sistema Reativo |
|---------|------------------|-----------------|
| **Iniciação** | Operador criava conversas | Usuário inicia enviando mensagem |
| **Interface** | Foco em criar/conectar | Foco em responder/monitorar |
| **Fluxo** | Proativo (empresa → usuário) | Reativo (usuário → empresa) |
| **Configuração** | Manual via interface | Automática via webhooks |
| **Templates** | Necessários para iniciar | Não necessários |

## 🏗️ Arquitetura do Sistema

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Usuário       │    │   Twilio         │    │   Sistema       │
│   WhatsApp      │    │   Platform       │    │   Reativo       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. Envia mensagem     │                       │
         ├──────────────────────►│                       │
         │                       │ 2. Webhook            │
         │                       ├──────────────────────►│
         │                       │                       │ 3. Cria conversa
         │                       │                       │    automaticamente
         │                       │                       │
         │                       │ 4. Operador responde │
         │                       │◄──────────────────────┤
         │ 5. Recebe resposta    │                       │
         │◄──────────────────────┤                       │
```

## 🚀 Configuração Rápida

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar Variáveis
```bash
cp .env.example .env
# Edite .env com suas credenciais
```

### 3. Iniciar Sistema Completo
```bash
./setup-reactive-ngrok.sh
```

Este script:
- ✅ Inicia o servidor Node.js
- ✅ Configura túnel ngrok
- ✅ Gera URLs dos webhooks
- ✅ Fornece instruções de configuração

## 🌐 Configuração de Webhooks

### WhatsApp Sender
1. Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
2. Encontre seu WhatsApp Sender registrado
3. Configure:
   - **Webhook URL**: `https://seu-ngrok.ngrok.io/webhooks/whatsapp/incoming`
   - **Status callback URL**: `https://seu-ngrok.ngrok.io/webhooks/whatsapp/status`
   - **HTTP Method**: POST

### Conversations Service
1. Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
2. Selecione seu Conversations Service
3. Configure:
   - **Webhook URL**: `https://seu-ngrok.ngrok.io/webhooks/conversations`
   - **HTTP Method**: POST
   - **Events**: 
     - ✅ onMessageAdded
     - ✅ onTypingStarted
     - ✅ onTypingEnded

## 👨‍💼 Interface do Operador

### Status do Sistema
- **Conexão Twilio**: Mostra se está conectado
- **WhatsApp Sender**: Status do número registrado
- **Webhooks**: Verifica se estão funcionando

### Conversas Ativas
- Lista todas as conversas em andamento
- Atualização automática a cada 10 segundos
- Clique para abrir chat individual

### Chat Ativo
- Mensagens da conversa selecionada
- Campo para responder
- Typing indicator bidirecional
- Informações da conversa

## 🔧 Fluxo Técnico

### 1. Recebimento de Mensagem
```javascript
// routes/webhooks.js
router.post('/whatsapp/incoming', async (req, res) => {
    // Verifica se é nova conversa
    if (!ConversationSid) {
        // Cria conversa automaticamente
        await createAutomaticConversation(From, Body);
    }
    // Responde com TwiML vazio
});
```

### 2. Criação Automática de Conversa
```javascript
async function createAutomaticConversation(whatsappNumber, firstMessage) {
    // 1. Verifica se já existe conversa
    // 2. Se não existe, cria nova conversa
    // 3. Adiciona participante WhatsApp
    // 4. Registra logs
}
```

### 3. Interface Reativa
```javascript
// public/script.js
function startConversationsMonitoring() {
    // Atualiza lista a cada 10 segundos
    setInterval(refreshConversations, 10000);
}
```

## 📊 Monitoramento e Logs

### Logs do Sistema
- **Arquivo**: `logs/twilio-YYYY-MM-DD.log`
- **Tipos**: webhook_received, message_received, conversation_created
- **Interface**: Seção "Logs do Sistema" na web

### Logs do ngrok
- **Arquivo**: `ngrok.log`
- **Comando**: `tail -f ngrok.log`

### Verificação de Status
```bash
# Testar webhooks
curl https://seu-ngrok.ngrok.io/webhooks/test

# Ver logs recentes
curl https://seu-ngrok.ngrok.io/webhooks/logs

# Verificar saúde do sistema
curl http://localhost:3000/health
```

## 🧪 Testes

### Teste Completo do Sistema
```bash
./test-reactive-system.sh
```

### Teste Manual
1. Acesse: http://localhost:3000
2. Clique em "Conectar como Operador"
3. Envie mensagem WhatsApp para seu número registrado
4. Veja conversa aparecer na interface
5. Responda através da web
6. Teste typing indicator

## 🐛 Troubleshooting

### Problema: Webhooks não funcionam
**Solução**:
```bash
# Verificar ngrok
curl https://seu-ngrok.ngrok.io/webhooks/test

# Reconfigurar ngrok
./setup-reactive-ngrok.sh
```

### Problema: Conversas não aparecem
**Verificar**:
1. ✅ Webhooks configurados na Twilio
2. ✅ ngrok ativo
3. ✅ Operador conectado na interface

### Problema: Mensagens não chegam
**Verificar**:
1. ✅ WhatsApp Sender configurado
2. ✅ Webhook URL correto
3. ✅ Logs mostram recebimento

## 📁 Arquivos Movidos para /old

- `test-conversation.sh` - Testes do sistema anterior
- `test-intelligent-conversation.sh` - Testes inteligentes
- `setup-whatsapp-sender.sh` - Configuração manual
- `webhook-urls-*.txt` - URLs antigas

## 🎯 Próximos Passos

1. **Configure os webhooks** usando as URLs geradas
2. **Teste o sistema** enviando mensagens
3. **Monitore os logs** para debug
4. **Treine operadores** na nova interface
5. **Documente processos** específicos da empresa

---

**Sistema Reativo Ativo!** 🚀
O sistema agora funciona de forma completamente automática e reativa.
