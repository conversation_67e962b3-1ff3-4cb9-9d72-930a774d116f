# 📋 Guia de Configuração da Twilio

Este guia detalha todos os passos necessários para configurar a Twilio para usar o projeto de teste do Typing Indicator com WhatsApp.

## 🎯 Visão Geral

Para usar este projeto, você precisará configurar:

1. **Conta Twilio** - Credenciais básicas
2. **API Keys** - Para gerar tokens de acesso
3. **Conversations Service** - Para gerenciar conversas
4. **WhatsApp Sandbox** - Para testar com WhatsApp
5. **Configurações de Webhook** (opcional)

## 🔑 1. Credenciais Básicas da Twilio

### 1.1 Obter Account SID e Auth Token

1. Acesse o [Twilio Console](https://console.twilio.com)
2. Faça login na sua conta
3. No dashboard principal, você verá:
   - **Account SID**: Começa com `AC...`
   - **Auth Token**: Clique em "Show" para revelar

```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
```

### 1.2 Verificar Configuração

Teste se as credenciais estão funcionando:

```bash
curl -X GET "https://api.twilio.com/2010-04-01/Accounts.json" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here
```

## 🔐 2. Criar API Key e Secret

As API Keys são necessárias para gerar tokens de acesso para o Conversations SDK.

### 2.1 Criar Nova API Key

1. No Twilio Console, vá para **Settings > API Keys**
2. Clique em **Create new API Key**
3. Preencha:
   - **Friendly Name**: `WhatsApp Typing Test API Key`
   - **Key Type**: `Standard`
4. Clique em **Create API Key**
5. **IMPORTANTE**: Copie o **Secret** imediatamente (não será mostrado novamente)

```env
TWILIO_API_KEY=SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_API_SECRET=your_api_secret_here
```

### 2.2 Verificar API Key

Teste a API Key:

```bash
curl -X GET "https://api.twilio.com/2010-04-01/Accounts.json" \
  -u SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_api_secret_here
```

## 💬 3. Configurar Conversations Service

### 3.1 Criar Conversations Service

1. No Twilio Console, vá para **Conversations > Services**
2. Clique em **Create new Service**
3. Preencha:
   - **Friendly Name**: `WhatsApp Typing Test Service`
   - **Unique Name**: `whatsapp-typing-test` (opcional)
4. Clique em **Create**
5. Copie o **Service SID** (começa com `IS...`)

```env
TWILIO_CONVERSATIONS_SERVICE_SID=ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 3.2 Configurar Service (Opcional)

Para otimizar o typing indicator, você pode ajustar o timeout:

1. No service criado, vá para **Configuration**
2. Ajuste **Typing Indicator Timeout** para `5` segundos
3. Salve as alterações

### 3.3 Verificar Service

```bash
curl -X GET "https://conversations.twilio.com/v1/Services/ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here
```

## 📱 4. Configurar WhatsApp Sandbox

### 4.1 Ativar WhatsApp Sandbox

1. No Twilio Console, vá para **Messaging > Try it out > Send a WhatsApp message**
2. Você verá:
   - **Sandbox Number**: Geralmente `+1 415 523 8886`
   - **Join Code**: Sua palavra-chave única (ex: `join happy-dog`)

```env
TWILIO_WHATSAPP_SANDBOX_NUMBER=whatsapp:+***********
TWILIO_SANDBOX_KEYWORD=happy-dog
```

### 4.2 Conectar seu WhatsApp

1. No seu WhatsApp, envie uma mensagem para `+1 415 523 8886`
2. Digite: `join [sua-palavra-chave]` (ex: `join happy-dog`)
3. Você receberá uma confirmação

### 4.3 Configurar Webhook (Opcional)

Para receber webhooks de mensagens:

1. Na página do Sandbox, em **Webhook URL for Incoming Messages**
2. Digite: `https://seu-dominio.com/webhook/whatsapp` (se aplicável)
3. Para teste local, deixe em branco

### 4.4 Testar Sandbox

```bash
curl -X POST "https://api.twilio.com/2010-04-01/Accounts/ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Messages.json" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here \
  -d "From=whatsapp:+***********" \
  -d "To=whatsapp:+*************" \
  -d "Body=Teste do sandbox"
```

## 🔧 5. Configurações Avançadas

### 5.1 Configurar Address Configuration

Para usar WhatsApp com Conversations, configure um Address Configuration:

```bash
curl -X POST "https://conversations.twilio.com/v1/Services/ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Configuration/Addresses" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here \
  -d "Type=whatsapp" \
  -d "Address=whatsapp:+***********" \
  -d "FriendlyName=WhatsApp Sandbox"
```

### 5.2 Configurar Webhooks do Service

Para receber eventos de typing:

```bash
curl -X POST "https://conversations.twilio.com/v1/Services/ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Configuration/Webhooks" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here \
  -d "Target=webhook" \
  -d "Webhook.Url=https://seu-dominio.com/webhook/conversations" \
  -d "Webhook.Method=POST" \
  -d "Webhook.Filters=onMessageAdded,onTypingStarted,onTypingEnded"
```

## ✅ 6. Verificação Final

### 6.1 Checklist de Configuração

- [ ] Account SID e Auth Token obtidos
- [ ] API Key e Secret criados
- [ ] Conversations Service criado
- [ ] WhatsApp Sandbox ativado
- [ ] Número WhatsApp conectado ao sandbox
- [ ] Arquivo `.env` configurado

### 6.2 Testar Configuração

Execute o endpoint de verificação da aplicação:

```bash
curl -X GET "http://localhost:3000/api/auth/verify"
```

Resposta esperada:
```json
{
  "status": "OK",
  "accountSid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "accountStatus": "active",
  "friendlyName": "Sua Conta Twilio",
  "hasConversationsService": true,
  "hasApiCredentials": true
}
```

## 🚨 7. Troubleshooting

### 7.1 Problemas Comuns

**Erro: "Invalid credentials"**
- Verifique se Account SID e Auth Token estão corretos
- Confirme se não há espaços extras nas variáveis

**Erro: "Service not found"**
- Verifique se o Service SID está correto
- Confirme se o service foi criado na conta correta

**WhatsApp não recebe mensagens**
- Confirme se o número está conectado ao sandbox
- Verifique se o formato do número está correto (+*************)
- Teste enviando uma mensagem manual pelo Console

**Typing indicator não funciona**
- Confirme se o Conversations Service está configurado
- Verifique se ambos os participantes estão na mesma conversa
- Teste com diferentes dispositivos/navegadores

### 7.2 Logs Úteis

Para debug, monitore os logs da Twilio:

1. **Console > Monitor > Logs > Errors**
2. **Console > Conversations > Logs**
3. **Console > Messaging > Logs**

### 7.3 Comandos de Debug

Listar conversas:
```bash
curl -X GET "https://conversations.twilio.com/v1/Services/ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Conversations" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here
```

Listar participantes de uma conversa:
```bash
curl -X GET "https://conversations.twilio.com/v1/Services/ISxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Conversations/CHxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/Participants" \
  -u ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx:your_auth_token_here
```

## 📚 8. Recursos Adicionais

### 8.1 Documentação Oficial

- [Twilio Conversations](https://www.twilio.com/docs/conversations)
- [WhatsApp Business API](https://www.twilio.com/docs/whatsapp)
- [Typing Indicator](https://www.twilio.com/docs/conversations/typing-indicator)
- [API Keys](https://www.twilio.com/docs/iam/keys)

### 8.2 Ferramentas Úteis

- [Twilio CLI](https://www.twilio.com/docs/twilio-cli)
- [Postman Collection](https://www.postman.com/twilio)
- [Twilio Helper Libraries](https://www.twilio.com/docs/libraries)

### 8.3 Limites e Considerações

- **Sandbox**: Limitado a números pré-aprovados
- **Rate Limits**: Consulte a documentação para limites de API
- **Typing Timeout**: Padrão de 5 segundos entre sinais
- **Message Limits**: Sandbox tem limites de mensagens por dia

---

## 🆘 Suporte

Se você encontrar problemas durante a configuração:

1. **Consulte os logs** no Twilio Console
2. **Verifique a documentação** oficial da Twilio
3. **Teste cada componente** individualmente
4. **Entre em contato** com o suporte da Twilio se necessário

**Importante**: Mantenha suas credenciais seguras e nunca as compartilhe publicamente!
