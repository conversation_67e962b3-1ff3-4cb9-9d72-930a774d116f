# URLs dos Webhooks para WhatsApp Sender - <PERSON><PERSON><PERSON> em Wed Jun  4 11:08:00 -03 2025
# WhatsApp Sender: whatsapp:+18382700077
# Configure estas URLs no Twilio Console

## WhatsApp Sender Configuration
Webhook URL: https://twilio-1749046051.loca.lt/webhooks/whatsapp/incoming
Status callback URL: https://twilio-1749046051.loca.lt/webhooks/whatsapp/status

## Conversations Service  
Webhook URL: https://twilio-1749046051.loca.lt/webhooks/conversations

## URLs para teste
Público: https://twilio-1749046051.loca.lt
Teste: https://twilio-1749046051.loca.lt/webhooks/test
Logs: https://twilio-1749046051.loca.lt/webhooks/logs

## Como configurar no Twilio Console:

### 1. Configurar WhatsApp Sender:
   - Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
   - Encontre o sender: +18382700077
   - Configure:
     * Webhook URL: https://twilio-1749046051.loca.lt/webhooks/whatsapp/incoming
     * Status callback URL: https://twilio-1749046051.loca.lt/webhooks/whatsapp/status
     * HTTP Method: POST

### 2. Configurar Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione seu service: IS44fe3b1fffce48d8bc5b6ab043cb771b
   - Configure:
     * Webhook URL: https://twilio-1749046051.loca.lt/webhooks/conversations
     * HTTP Method: POST
     * Events: onMessageAdded, onTypingStarted, onTypingEnded

## Comandos úteis:
- Ver logs: curl https://twilio-1749046051.loca.lt/webhooks/logs
- Testar webhook: curl https://twilio-1749046051.loca.lt/webhooks/test
- Limpar participantes: curl -X DELETE https://twilio-1749046051.loca.lt/api/conversations/cleanup-whatsapp/+5551993590210

## Diferenças do Sandbox:
- ✅ Não precisa de "join" keyword
- ✅ Pode enviar para qualquer número
- ✅ Sem limitações de sandbox
- ✅ Mais estável e confiável
