
> twilio-whatsapp-chat-system@2.0.0 start
> node server.js

🚀 Servidor rodando na porta 3000
📱 Acesse: http://localhost:3000
🔧 Ambiente: development
✅ Todas as variáveis de ambiente configuradas
🔚 Encerrando todas as conversas ativas...
📋 Encontradas 2 conversas ativas
✅ Conversa CHfd2af8a571d74915a9f3f07fd37d5aa5 encerrada
✅ Conversa CH2e56ab5317ae4033953c47377b38da87 encerrada
🎉 2 conversas encerradas com sucesso
Conectando WhatsApp: whatsapp:+5551993590210 para usuário: teste-unitario-1
Criando nova conversa...
Adicionando participante chat: teste-unitario-1
Adicionando participante WhatsApp: whatsapp:+5551993590210
🧹 Limpando participante WhatsApp de outras conversas...
🔧 Tentando criar participante com Número Registrado:
   Address: whatsapp:+5551993590210
   Proxy: whatsapp:+***********
   Conversation: CHef37e6a30e024ae48eb1912565fc06b9
❌ Falha com Número Registrado: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e
⚠️ Participante já existe, tentando próximo proxy...
🔧 Tentando criar participante com Sandbox (Fallback):
   Address: whatsapp:+5551993590210
   Proxy: whatsapp:+14155238886
   Conversation: CHef37e6a30e024ae48eb1912565fc06b9
✅ Participante criado com Sandbox (Fallback):
   SID: MB4cac53aa92614336a67b43b64b36431c
   Address: whatsapp:+5551993590210
   Proxy: undefined
   Type: whatsapp
⚠️ Proxy não definido com Sandbox (Fallback), tentando próximo...
🗑️ Participante sem proxy removido
Erro ao conectar conversa WhatsApp: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/conversations.js:444:27)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/conversations.js:448:45
[2025-06-04T18:15:33.557Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/whatsapp/incoming',
  headers: {
    'content-type': 'application/x-www-form-urlencoded',
    'user-agent': 'curl/8.7.1',
    'x-twilio-signature': undefined
  },
  body: {
    From: 'whatsapp: *************',
    To: 'whatsapp: ***********',
    Body: 'Teste unitario 2 webhook',
    MessageSid: 'UNIT_TEST_2_1749060933',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    ApiVersion: '2010-04-01'
  },
  query: {}
}
[2025-06-04T18:15:33.558Z] MESSAGE_RECEIVED: {
  messageSid: 'UNIT_TEST_2_1749060933',
  from: 'whatsapp: *************',
  to: 'whatsapp: ***********',
  body: 'Teste unitario 2 webhook',
  conversationSid: undefined,
  participantSid: undefined,
  author: undefined
}
📱 Mensagem WhatsApp recebida: {
  from: 'whatsapp: *************',
  to: 'whatsapp: ***********',
  body: 'Teste unitario 2 webhook',
  conversationSid: undefined
}
🔄 Criando conversa automática para: whatsapp: *************
🔍 Número formatado: whatsapp:+*************
📝 Criando nova conversa: Chat WhatsApp +************* - 6/4/2025, 3:15:41 PM
👤 Adicionando participante WhatsApp: whatsapp:+*************
🔧 Tentando criar participante com Número Registrado:
   Address: whatsapp:+*************
   Proxy: whatsapp:+***********
   Conversation: CH3941adcaf76f49519e3d302f2c1085a9
❌ Falha com Número Registrado: Invalid messaging binding address
🔧 Tentando criar participante com Sandbox (Fallback):
   Address: whatsapp:+*************
   Proxy: whatsapp:+14155238886
   Conversation: CH3941adcaf76f49519e3d302f2c1085a9
❌ Falha com Sandbox (Fallback): Invalid messaging binding address
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
[2025-06-04T18:15:42.444Z] ERROR: {
  message: 'Erro ao criar conversa automática',
  stack: 'Error: Falha ao criar participante com todos os proxies disponíveis\n' +
    '    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n' +
    '    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17',
  context: { from: 'whatsapp: *************', body: 'Teste unitario 2 webhook' }
}
📱 Enviando mensagem direta WhatsApp:
   De: whatsapp:+***********
   Para: whatsapp:+5551993590210
   Mensagem: 🧪 TESTE UNITÁRIO 3 - Mensagem direta funcionando!
🔧 Tentando enviar com Número Registrado: whatsapp:+***********
✅ Mensagem enviada com Número Registrado!
   Message SID: SMa1713fab5fd684182b7c326dadc76791
   Status: queued
[2025-06-04T18:15:48.208Z] MESSAGE_SENT: {
  conversationSid: undefined,
  messageSid: 'SMa1713fab5fd684182b7c326dadc76791',
  body: '🧪 TESTE UNITÁRIO 3 - Mensagem direta funcionando!',
  author: undefined,
  to: 'whatsapp:+5551993590210'
}
📱 Enviando mensagem direta WhatsApp:
   De: whatsapp:+***********
   Para: whatsapp:+5551993590210
   Mensagem: 🎉 SISTEMA FINAL FUNCIONANDO! Esta mensagem deve chegar no seu WhatsApp!
🔧 Tentando enviar com Número Registrado: whatsapp:+***********
✅ Mensagem enviada com Número Registrado!
   Message SID: SM3248a300156f365bb479f697170ae2e3
   Status: queued
[2025-06-04T18:16:54.857Z] MESSAGE_SENT: {
  conversationSid: undefined,
  messageSid: 'SM3248a300156f365bb479f697170ae2e3',
  body: '🎉 SISTEMA FINAL FUNCIONANDO! Esta mensagem deve chegar no seu WhatsApp!',
  author: undefined,
  to: 'whatsapp:+5551993590210'
}
[2025-06-04T18:35:25.165Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/conversations',
  headers: {
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': 'nCW+1slzEqgocqzVG1ZcYfbhpYw='
  },
  body: {
    RoleSid: 'RL1ba48e8ab27348f8a97d644944a083e1',
    ClientIdentity: 'operador-1',
    RetryCount: '0',
    EventType: 'onParticipantAdded',
    Attributes: '{}',
    DateCreated: '2025-06-04T18:35:24.794Z',
    ChatServiceSid: 'IS44fe3b1fffce48d8bc5b6ab043cb771b',
    Identity: 'operador-1',
    ParticipantSid: 'MB05ae734a94674f969beffc8464afa2cb',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    Source: 'SDK',
    ConversationSid: 'CHef37e6a30e024ae48eb1912565fc06b9'
  },
  query: {}
}
[2025-06-04T18:35:25.169Z] CONVERSATION_EVENT: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHef37e6a30e024ae48eb1912565fc06b9',
  participantSid: 'MB05ae734a94674f969beffc8464afa2cb',
  author: undefined,
  body: undefined,
  messageSid: undefined
}
💬 Evento de Conversations: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHef37e6a30e024ae48eb1912565fc06b9',
  author: undefined,
  body: undefined
}
🔚 Encerrando todas as conversas ativas...
📋 Encontradas 2 conversas ativas
✅ Conversa CHef37e6a30e024ae48eb1912565fc06b9 encerrada
✅ Conversa CH3941adcaf76f49519e3d302f2c1085a9 encerrada
🎉 2 conversas encerradas com sucesso
[2025-06-04T18:36:05.462Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/whatsapp/incoming',
  headers: {
    'content-type': 'application/x-www-form-urlencoded',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': 'qxFfuvzj6mC8+gD+PXGiN2sZiQk='
  },
  body: {
    SmsMessageSid: 'SM1402cf255599a1ad682706b5ae2e30b5',
    NumMedia: '0',
    ProfileName: 'Matheus Turnes',
    MessageType: 'text',
    SmsSid: 'SM1402cf255599a1ad682706b5ae2e30b5',
    WaId: '************',
    SmsStatus: 'received',
    Body: 'Oi',
    To: 'whatsapp:+***********',
    MessagingServiceSid: 'MG729145c77388289ea56a5854de648aea',
    NumSegments: '1',
    ReferralNumMedia: '0',
    MessageSid: 'SM1402cf255599a1ad682706b5ae2e30b5',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    From: 'whatsapp:+************',
    ApiVersion: '2010-04-01'
  },
  query: {}
}
[2025-06-04T18:36:05.465Z] MESSAGE_RECEIVED: {
  messageSid: 'SM1402cf255599a1ad682706b5ae2e30b5',
  from: 'whatsapp:+************',
  to: 'whatsapp:+***********',
  body: 'Oi',
  conversationSid: undefined,
  participantSid: undefined,
  author: undefined
}
📱 Mensagem WhatsApp recebida: {
  from: 'whatsapp:+************',
  to: 'whatsapp:+***********',
  body: 'Oi',
  conversationSid: undefined
}
🔄 Criando conversa automática para: whatsapp:+************
🔍 Número formatado: whatsapp:+************
📝 Criando nova conversa: Chat WhatsApp +************ - 6/4/2025, 3:36:14 PM
👤 Adicionando participante WhatsApp: whatsapp:+************
🔧 Tentando criar participante com Número Registrado:
   Address: whatsapp:+************
   Proxy: whatsapp:+***********
   Conversation: CHb971d03344354da78714e544c1d99941
❌ Falha com Número Registrado: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e
⚠️ Participante já existe, tentando próximo proxy...
🔧 Tentando criar participante com Sandbox (Fallback):
   Address: whatsapp:+************
   Proxy: whatsapp:+14155238886
   Conversation: CHb971d03344354da78714e544c1d99941
✅ Participante criado com Sandbox (Fallback):
   SID: MB0d8b0a4366de482d8d5954821e351a5a
   Address: whatsapp:+5551993590210
   Proxy: undefined
⚠️ Proxy não definido com Sandbox (Fallback), tentando próximo...
🗑️ Participante sem proxy removido
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
[2025-06-04T18:36:16.150Z] ERROR: {
  message: 'Erro ao criar conversa automática',
  stack: 'Error: Falha ao criar participante com todos os proxies disponíveis\n' +
    '    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n' +
    '    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17',
  context: { from: 'whatsapp:+************', body: 'Oi' }
}
[2025-06-04T18:36:18.506Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/conversations',
  headers: {
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': 'nMNCdchhYqDveyuDJ5qsI2Dg3lM='
  },
  body: {
    RoleSid: 'RL1ba48e8ab27348f8a97d644944a083e1',
    ClientIdentity: 'operador-1',
    RetryCount: '0',
    EventType: 'onParticipantAdded',
    Attributes: '{}',
    DateCreated: '2025-06-04T18:36:18.109Z',
    ChatServiceSid: 'IS44fe3b1fffce48d8bc5b6ab043cb771b',
    Identity: 'operador-1',
    ParticipantSid: 'MB61074e929cab477f9cfaf3629e487565',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    Source: 'SDK',
    ConversationSid: 'CHb971d03344354da78714e544c1d99941'
  },
  query: {}
}
[2025-06-04T18:36:18.507Z] CONVERSATION_EVENT: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHb971d03344354da78714e544c1d99941',
  participantSid: 'MB61074e929cab477f9cfaf3629e487565',
  author: undefined,
  body: undefined,
  messageSid: undefined
}
💬 Evento de Conversations: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHb971d03344354da78714e544c1d99941',
  author: undefined,
  body: undefined
}
[2025-06-04T18:36:50.634Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/conversations',
  headers: {
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': '9jouSFw537UvIfSunqKmYeAtQ3k='
  },
  body: {
    MessagingServiceSid: 'MG6f2e9d65b7358805a8c82f68ae38c466',
    EventType: 'onMessageAdded',
    Attributes: '{}',
    DateCreated: '2025-06-04T18:36:50.352Z',
    Index: '0',
    ChatServiceSid: 'IS44fe3b1fffce48d8bc5b6ab043cb771b',
    MessageSid: 'IMe216c09095054f5da9b09e5e24de52ac',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    Source: 'SDK',
    ClientIdentity: 'operador-1',
    RetryCount: '0',
    Author: 'operador-1',
    ParticipantSid: 'MB61074e929cab477f9cfaf3629e487565',
    Body: 'cadê minha mensagem',
    ConversationSid: 'CHb971d03344354da78714e544c1d99941'
  },
  query: {}
}
[2025-06-04T18:36:50.636Z] CONVERSATION_EVENT: {
  eventType: 'onMessageAdded',
  conversationSid: 'CHb971d03344354da78714e544c1d99941',
  participantSid: 'MB61074e929cab477f9cfaf3629e487565',
  author: 'operador-1',
  body: 'cadê minha mensagem',
  messageSid: 'IMe216c09095054f5da9b09e5e24de52ac'
}
💬 Evento de Conversations: {
  eventType: 'onMessageAdded',
  conversationSid: 'CHb971d03344354da78714e544c1d99941',
  author: 'operador-1',
  body: 'cadê minha mensagem'
}
📝 Nova mensagem na conversa: cadê minha mensagem
[2025-06-04T18:37:03.009Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/whatsapp/incoming',
  headers: {
    'content-type': 'application/x-www-form-urlencoded',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': '/AkPXuWlNd8MmW2Aq/bJSlhO3l0='
  },
  body: {
    SmsMessageSid: 'SM26853bfd89bf30c96408b0b612aad704',
    NumMedia: '0',
    ProfileName: 'Matheus Turnes',
    MessageType: 'text',
    SmsSid: 'SM26853bfd89bf30c96408b0b612aad704',
    WaId: '************',
    SmsStatus: 'received',
    Body: 'Oiii',
    To: 'whatsapp:+***********',
    MessagingServiceSid: 'MG729145c77388289ea56a5854de648aea',
    NumSegments: '1',
    ReferralNumMedia: '0',
    MessageSid: 'SM26853bfd89bf30c96408b0b612aad704',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    From: 'whatsapp:+************',
    ApiVersion: '2010-04-01'
  },
  query: {}
}
[2025-06-04T18:37:03.011Z] MESSAGE_RECEIVED: {
  messageSid: 'SM26853bfd89bf30c96408b0b612aad704',
  from: 'whatsapp:+************',
  to: 'whatsapp:+***********',
  body: 'Oiii',
  conversationSid: undefined,
  participantSid: undefined,
  author: undefined
}
📱 Mensagem WhatsApp recebida: {
  from: 'whatsapp:+************',
  to: 'whatsapp:+***********',
  body: 'Oiii',
  conversationSid: undefined
}
🔄 Criando conversa automática para: whatsapp:+************
🔍 Número formatado: whatsapp:+************
📝 Criando nova conversa: Chat WhatsApp +************ - 6/4/2025, 3:37:12 PM
👤 Adicionando participante WhatsApp: whatsapp:+************
🔧 Tentando criar participante com Número Registrado:
   Address: whatsapp:+************
   Proxy: whatsapp:+***********
   Conversation: CHc05551abf794482b8b874e8ac37185b8
❌ Falha com Número Registrado: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e
⚠️ Participante já existe, tentando próximo proxy...
🔧 Tentando criar participante com Sandbox (Fallback):
   Address: whatsapp:+************
   Proxy: whatsapp:+14155238886
   Conversation: CHc05551abf794482b8b874e8ac37185b8
✅ Participante criado com Sandbox (Fallback):
   SID: MB4c4f7ff035494c68abb19dbf49a16d64
   Address: whatsapp:+5551993590210
   Proxy: undefined
⚠️ Proxy não definido com Sandbox (Fallback), tentando próximo...
🗑️ Participante sem proxy removido
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
Erro ao criar conversa automática: Error: Falha ao criar participante com todos os proxies disponíveis
    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)
    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17
[2025-06-04T18:37:13.761Z] ERROR: {
  message: 'Erro ao criar conversa automática',
  stack: 'Error: Falha ao criar participante com todos os proxies disponíveis\n' +
    '    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n' +
    '    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17',
  context: { from: 'whatsapp:+************', body: 'Oiii' }
}
[2025-06-04T18:37:20.024Z] WEBHOOK_RECEIVED: {
  method: 'POST',
  url: '/conversations',
  headers: {
    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'user-agent': 'TwilioProxy/1.1',
    'x-twilio-signature': 'CWvWemD2mvUmHBob/yKnPAdi4qE='
  },
  body: {
    RoleSid: 'RL1ba48e8ab27348f8a97d644944a083e1',
    ClientIdentity: 'operador-1',
    RetryCount: '0',
    EventType: 'onParticipantAdded',
    Attributes: '{}',
    DateCreated: '2025-06-04T18:37:19.653Z',
    ChatServiceSid: 'IS44fe3b1fffce48d8bc5b6ab043cb771b',
    Identity: 'operador-1',
    ParticipantSid: 'MB527c82b889cc464aad41b3048accfee9',
    AccountSid: 'AC04266eaa4a7caf821e8dac9d92879e95',
    Source: 'SDK',
    ConversationSid: 'CHc05551abf794482b8b874e8ac37185b8'
  },
  query: {}
}
[2025-06-04T18:37:20.026Z] CONVERSATION_EVENT: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHc05551abf794482b8b874e8ac37185b8',
  participantSid: 'MB527c82b889cc464aad41b3048accfee9',
  author: undefined,
  body: undefined,
  messageSid: undefined
}
💬 Evento de Conversations: {
  eventType: 'onParticipantAdded',
  conversationSid: 'CHc05551abf794482b8b874e8ac37185b8',
  author: undefined,
  body: undefined
}
