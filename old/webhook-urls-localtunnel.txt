# URLs dos Webhooks LocalTunnel - Gerado em Wed Jun  4 11:07:38 -03 2025
# Configure estas URLs no Twilio Console

## WhatsApp Sandbox
When a message comes in: https://twilio-1749046051.loca.lt/webhooks/whatsapp/incoming
Status callback URL: https://twilio-1749046051.loca.lt/webhooks/whatsapp/status

## Conversations Service  
Webhook URL: https://twilio-1749046051.loca.lt/webhooks/conversations

## URLs para teste
Público: https://twilio-1749046051.loca.lt
Teste: https://twilio-1749046051.loca.lt/webhooks/test
Logs: https://twilio-1749046051.loca.lt/webhooks/logs

## Como configurar:
1. WhatsApp Sandbox:
   - Acesse: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
   - Configure "When a message comes in": https://twilio-1749046051.loca.lt/webhooks/whatsapp/incoming
   - Configure "Status callback URL": https://twilio-1749046051.loca.lt/webhooks/whatsapp/status

2. Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione seu service
   - Configure "Webhook URL": https://twilio-1749046051.loca.lt/webhooks/conversations
   - Marque eventos: onMessageAdded, onTypingStarted, onTypingEnded

## Comandos úteis:
- Parar túnel: pkill -f 'lt --port'
- Ver logs: tail -f localtunnel.log
- Novo túnel: ./setup-localtunnel.sh

## IMPORTANTE:
- LocalTunnel pode pedir para "Click to Continue" na primeira vez
- Acesse https://twilio-1749046051.loca.lt no navegador se os webhooks não funcionarem
- O subdomínio muda a cada execução do script
