# URLs dos Webhooks - <PERSON><PERSON><PERSON> em Wed Jun  4 15:15:09 -03 2025
# Configure estas URLs no Twilio Console

## WhatsApp Sandbox
When a message comes in: https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming
Status callback URL: https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/status

## Conversations Service  
Webhook URL: https://e990-200-194-249-22.ngrok-free.app/webhooks/conversations

## URLs para teste
Público: https://e990-200-194-249-22.ngrok-free.app
Teste: https://e990-200-194-249-22.ngrok-free.app/webhooks/test
Logs: https://e990-200-194-249-22.ngrok-free.app/webhooks/logs

## Como configurar:
1. Acesse: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
2. Configure "When a message comes in" com: https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming
3. Configure "Status callback URL" com: https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/status
4. Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
5. Selecione seu service e configure Webhook URL: https://e990-200-194-249-22.ngrok-free.app/webhooks/conversations
