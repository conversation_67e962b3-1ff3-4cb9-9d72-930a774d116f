{"name": "twilio-whatsapp-chat-system", "version": "2.0.0", "description": "Sistema de chat WhatsApp reativo integrado com Twilio Conversations API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "./test-all.sh", "test:reactive": "./test-reactive-system.sh", "start:mac": "./start-mac.sh", "dev:mac": "./dev-mac.sh", "test:mac": "./test-all.sh", "setup": "npm install && cp .env.example .env", "verify": "curl -s http://localhost:3000/api/auth/verify | jq ."}, "keywords": ["twi<PERSON>", "whatsapp", "conversations", "chat", "reactive", "webhook", "typing-indicator"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "twilio": "^4.19.0", "body-parser": "^1.20.2", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}