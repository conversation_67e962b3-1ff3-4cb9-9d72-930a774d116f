#!/bin/bash

# Diagnóstico completo e testes unitários
# Identifica todos os problemas do sistema

echo "🔍 DIAGNÓSTICO COMPLETO - Testes Unitários"
echo "=========================================="

cd /Users/<USER>/Documents/augment-projects/twiliowhats

echo ""
echo "📋 1. Verificando Status do Sistema"
echo "=================================="

# Verificar servidor
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Servidor rodando"
else
    echo "❌ Servidor não está rodando"
    exit 1
fi

# Verificar ngrok (necessário para webhooks reais)
NGROK_STATUS=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null)
if echo "$NGROK_STATUS" | grep -q "https://"; then
    NGROK_URL=$(echo "$NGROK_STATUS" | jq -r '.tunnels[0].public_url' 2>/dev/null)
    echo "✅ ngrok ativo: $NGROK_URL"
else
    echo "❌ ngrok não está rodando - NECESSÁRIO para webhooks reais"
fi

echo ""
echo "📋 2. Testando APIs Básicas"
echo "=========================="

# Teste 1: Autenticação
AUTH_TEST=$(curl -s http://localhost:3000/api/auth/verify)
if echo "$AUTH_TEST" | grep -q '"status":"OK"'; then
    echo "✅ Teste 1: Autenticação Twilio"
else
    echo "❌ Teste 1: Falha na autenticação"
    echo "   Resposta: $AUTH_TEST"
fi

# Teste 2: Listagem de conversas
CONV_LIST=$(curl -s http://localhost:3000/api/conversations)
if echo "$CONV_LIST" | grep -q '"conversations"'; then
    CONV_COUNT=$(echo "$CONV_LIST" | jq '.conversations | length' 2>/dev/null || echo "0")
    echo "✅ Teste 2: Listagem de conversas ($CONV_COUNT ativas)"
else
    echo "❌ Teste 2: Falha na listagem"
    echo "   Resposta: $CONV_LIST"
fi

# Teste 3: Webhook de teste
WEBHOOK_TEST=$(curl -s http://localhost:3000/webhooks/test)
if echo "$WEBHOOK_TEST" | grep -q "funcionando"; then
    echo "✅ Teste 3: Webhooks básicos"
else
    echo "❌ Teste 3: Falha nos webhooks"
fi

echo ""
echo "📋 3. Testando Conversa Específica"
echo "================================="

CONV_SID="CH9276f12173d24c83977fd6675cb10f28"
echo "🔍 Analisando conversa: $CONV_SID"

# Verificar se conversa existe
CONV_DETAILS=$(curl -s "http://localhost:3000/api/conversations/$CONV_SID")
if echo "$CONV_DETAILS" | grep -q '"conversationSid"'; then
    echo "✅ Conversa existe"
    
    # Verificar participantes
    echo ""
    echo "👥 Verificando participantes..."
    
    # Criar script para verificar participantes via Twilio diretamente
    cat > check_participants.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function checkParticipants() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        const conversationSid = process.argv[2];
        
        console.log(`🔍 Verificando participantes da conversa: ${conversationSid}`);
        
        let participants;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participants = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .list();
        } else {
            participants = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .list();
        }

        console.log(`📋 Total de participantes: ${participants.length}`);
        
        participants.forEach((p, index) => {
            console.log(`\n👤 Participante ${index + 1}:`);
            console.log(`   SID: ${p.sid}`);
            console.log(`   Identity: ${p.identity || 'N/A'}`);
            
            if (p.messagingBinding) {
                console.log(`   📱 Messaging Binding:`);
                console.log(`      Address: ${p.messagingBinding.address || 'N/A'}`);
                console.log(`      Proxy: ${p.messagingBinding.proxyAddress || 'N/A'}`);
                console.log(`      Type: ${p.messagingBinding.type || 'N/A'}`);
                
                if (p.messagingBinding.address && p.messagingBinding.address.includes('whatsapp:')) {
                    if (p.messagingBinding.proxyAddress) {
                        console.log(`   ✅ Participante WhatsApp com proxy correto`);
                    } else {
                        console.log(`   ❌ Participante WhatsApp SEM proxy`);
                    }
                }
            } else {
                console.log(`   📱 Messaging Binding: Nenhum`);
            }
        });
        
        // Verificar se há participante WhatsApp válido
        const whatsappParticipants = participants.filter(p =>
            p.messagingBinding && 
            p.messagingBinding.address && 
            p.messagingBinding.address.includes('whatsapp:') &&
            p.messagingBinding.proxyAddress
        );
        
        console.log(`\n📊 Resumo:`);
        console.log(`   Total: ${participants.length}`);
        console.log(`   WhatsApp válidos: ${whatsappParticipants.length}`);
        
        if (whatsappParticipants.length === 0) {
            console.log(`\n❌ PROBLEMA: Nenhum participante WhatsApp válido encontrado`);
            console.log(`   Isso explica por que mensagens não funcionam`);
        } else {
            console.log(`\n✅ Participantes WhatsApp válidos encontrados`);
        }

    } catch (error) {
        console.error('❌ Erro ao verificar participantes:', error.message);
    }
}

checkParticipants();
EOF

    # Executar verificação
    node check_participants.js "$CONV_SID"
    rm check_participants.js
    
else
    echo "❌ Conversa não existe ou não é acessível"
    echo "   Resposta: $CONV_DETAILS"
fi

echo ""
echo "📋 4. Testando Criação de Nova Conversa"
echo "======================================"

# Limpar conversas primeiro
echo "🧹 Limpando conversas existentes..."
CLEANUP=$(curl -s -X DELETE "http://localhost:3000/api/conversations/close-all/execute")
CLOSED_COUNT=$(echo "$CLEANUP" | jq -r '.successCount' 2>/dev/null || echo "0")
echo "✅ $CLOSED_COUNT conversas encerradas"

sleep 3

# Testar criação de nova conversa
echo ""
echo "📝 Testando criação de nova conversa..."

CREATE_TEST=$(curl -s -X POST "http://localhost:3000/api/conversations/connect-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "whatsappNumber": "+*************",
    "userIdentity": "teste-diagnostico-completo",
    "friendlyName": "Teste Diagnóstico Completo"
  }')

echo "📤 Resultado da criação:"
echo "$CREATE_TEST" | jq '.' 2>/dev/null || echo "$CREATE_TEST"

if echo "$CREATE_TEST" | grep -q '"success":true'; then
    NEW_CONV_SID=$(echo "$CREATE_TEST" | jq -r '.conversation.conversationSid' 2>/dev/null)
    PROXY_ADDR=$(echo "$CREATE_TEST" | jq -r '.participants.whatsapp.data.proxyAddress' 2>/dev/null)
    
    echo ""
    echo "✅ Nova conversa criada: $NEW_CONV_SID"
    echo "📱 Proxy Address: $PROXY_ADDR"
    
    if [ "$PROXY_ADDR" != "null" ] && [ "$PROXY_ADDR" != "" ]; then
        echo "✅ Proxy Address definido corretamente"
        
        # Testar envio de mensagem
        echo ""
        echo "💬 Testando envio de mensagem..."
        
        SEND_TEST=$(curl -s -X POST "http://localhost:3000/api/conversations/$NEW_CONV_SID/messages" \
          -H "Content-Type: application/json" \
          -d '{
            "body": "🧪 TESTE DIAGNÓSTICO - Esta mensagem deve funcionar!",
            "author": "teste-diagnostico-completo"
          }')
        
        if echo "$SEND_TEST" | grep -q '"messageSid"'; then
            MSG_SID=$(echo "$SEND_TEST" | jq -r '.messageSid' 2>/dev/null)
            echo "✅ Mensagem enviada: $MSG_SID"
            
            # Monitorar logs por 5 segundos
            echo "⏳ Monitorando entrega..."
            for i in {1..5}; do
                sleep 1
                if grep -q "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
                    echo "📋 Log encontrado:"
                    grep "$MSG_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -1
                    break
                fi
                echo -n "."
            done
            
        else
            echo "❌ Falha ao enviar mensagem"
            echo "$SEND_TEST"
        fi
        
    else
        echo "❌ Proxy Address não foi definido"
    fi
    
else
    echo "❌ Falha ao criar conversa"
fi

echo ""
echo "📋 5. Testando Webhook Real"
echo "========================="

# Testar webhook com dados reais
echo "📱 Testando webhook com simulação real..."

REAL_WEBHOOK=$(curl -s -X POST "http://localhost:3000/webhooks/whatsapp/incoming" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+*************&To=whatsapp:+***********&Body=Teste%20real%20de%20webhook&MessageSid=REAL_TEST_$(date +%s)&AccountSid=AC04266eaa4a7caf821e8dac9d92879e95&ApiVersion=2010-04-01")

echo "📤 Resposta do webhook: $REAL_WEBHOOK"

sleep 3

# Verificar se criou conversa
CONV_AFTER=$(curl -s http://localhost:3000/api/conversations)
CONV_COUNT_AFTER=$(echo "$CONV_AFTER" | jq '.conversations | length' 2>/dev/null || echo "0")
echo "📋 Conversas após webhook: $CONV_COUNT_AFTER"

echo ""
echo "📋 6. Verificando Logs de Erro"
echo "============================="

echo "📄 Últimos logs do sistema:"
if [ -f "logs/twilio-$(date +%Y-%m-%d).log" ]; then
    echo "🔍 Últimas 10 entradas:"
    tail -10 "logs/twilio-$(date +%Y-%m-%d).log" | while read line; do
        if echo "$line" | grep -q '"type":"error"'; then
            echo "❌ ERRO: $line"
        else
            echo "   $line"
        fi
    done
else
    echo "❌ Arquivo de log não encontrado"
fi

echo ""
echo "📋 7. Verificando Configuração Twilio"
echo "===================================="

# Verificar configuração do WhatsApp Sender
echo "📱 Verificando configuração do WhatsApp Sender..."

cat > check_twilio_config.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function checkTwilioConfig() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        console.log('🔍 Verificando configuração Twilio...');
        
        // Verificar WhatsApp Sender
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER;
        console.log(`📱 WhatsApp Sender configurado: ${senderNumber}`);
        
        // Verificar se o número está ativo
        try {
            const phoneNumbers = await client.incomingPhoneNumbers.list();
            const whatsappNumber = phoneNumbers.find(p => 
                p.phoneNumber === senderNumber?.replace('whatsapp:', '')
            );
            
            if (whatsappNumber) {
                console.log(`✅ Número WhatsApp encontrado na conta`);
                console.log(`   Status: ${whatsappNumber.status || 'N/A'}`);
                console.log(`   Capabilities: ${JSON.stringify(whatsappNumber.capabilities)}`);
            } else {
                console.log(`⚠️ Número WhatsApp não encontrado nos números da conta`);
            }
        } catch (error) {
            console.log(`⚠️ Erro ao verificar números: ${error.message}`);
        }
        
        // Verificar Conversations Service
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            try {
                const service = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .fetch();
                
                console.log(`✅ Conversations Service ativo: ${service.friendlyName}`);
                console.log(`   Webhook URL: ${service.webhookUrl || 'N/A'}`);
                
            } catch (error) {
                console.log(`❌ Erro no Conversations Service: ${error.message}`);
            }
        }

    } catch (error) {
        console.error('❌ Erro na verificação:', error.message);
    }
}

checkTwilioConfig();
EOF

node check_twilio_config.js
rm check_twilio_config.js

echo ""
echo "📋 RESUMO DO DIAGNÓSTICO"
echo "======================="

echo "🔍 Problemas identificados:"
echo "1. Verificar se participantes WhatsApp têm proxy correto"
echo "2. Verificar se webhooks estão configurados corretamente"
echo "3. Verificar se ngrok está rodando para webhooks reais"
echo "4. Verificar logs de erro para problemas específicos"

echo ""
echo "🔧 Próximas ações necessárias:"
echo "1. Corrigir participantes sem proxy"
echo "2. Configurar webhooks reais se necessário"
echo "3. Implementar testes unitários específicos"
echo "4. Validar fluxo completo de mensagens"
