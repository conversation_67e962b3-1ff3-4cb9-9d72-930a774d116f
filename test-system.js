#!/usr/bin/env node

/**
 * Testes unitários para o sistema WhatsApp Twilio
 * Uso: node test-system.js
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = `http://localhost:${process.env.PORT || 3000}`;
const TEST_WHATSAPP_NUMBER = '+5551993590210';

// Cores para output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName) {
    log(`\n🧪 Teste: ${testName}`, 'blue');
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function testServerHealth() {
    logTest('Verificar saúde do servidor');
    
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        
        if (response.status === 200) {
            logSuccess('Servidor está funcionando');
            log(`   Status: ${response.data.status}`);
            log(`   Ambiente: ${response.data.environment}`);
            return true;
        } else {
            logError(`Servidor retornou status ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Erro ao conectar com servidor: ${error.message}`);
        logWarning('Certifique-se de que o servidor está rodando com: ./start-server.sh');
        return false;
    }
}

async function testWebhooks() {
    logTest('Verificar webhooks');
    
    try {
        const response = await axios.get(`${BASE_URL}/webhooks/test`);
        
        if (response.status === 200) {
            logSuccess('Webhooks estão funcionando');
            log(`   Endpoints disponíveis:`);
            Object.entries(response.data.endpoints).forEach(([name, path]) => {
                log(`     - ${name}: ${path}`);
            });
            return true;
        } else {
            logError(`Webhooks retornaram status ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Erro ao testar webhooks: ${error.message}`);
        return false;
    }
}

async function testTokenGeneration() {
    logTest('Gerar token de acesso');
    
    try {
        const response = await axios.post(`${BASE_URL}/api/auth/token`, {
            identity: 'test-user'
        });
        
        if (response.status === 200 && response.data.token) {
            logSuccess('Token gerado com sucesso');
            log(`   Token: ${response.data.token.substring(0, 50)}...`);
            return response.data.token;
        } else {
            logError('Falha ao gerar token');
            return null;
        }
    } catch (error) {
        logError(`Erro ao gerar token: ${error.message}`);
        return null;
    }
}

async function testWhatsAppConnectivity() {
    logTest('Testar conectividade WhatsApp');
    
    try {
        const response = await axios.get(`${BASE_URL}/api/conversations/test-whatsapp?number=${TEST_WHATSAPP_NUMBER}`);
        
        if (response.status === 200) {
            const { summary, results } = response.data;
            
            if (summary.successful > 0) {
                logSuccess(`WhatsApp funcionando: ${summary.successful}/${summary.total} senders`);
                
                results.forEach(result => {
                    if (result.success) {
                        log(`   ✅ ${result.sender}: ${result.messageSid}`);
                    } else {
                        log(`   ❌ ${result.sender}: ${result.error}`);
                    }
                });
                
                return true;
            } else {
                logError('Nenhum sender WhatsApp funcionando');
                return false;
            }
        } else {
            logError(`Teste WhatsApp retornou status ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Erro ao testar WhatsApp: ${error.message}`);
        return false;
    }
}

async function testConversationCreation() {
    logTest('Criar conversa de teste');
    
    try {
        const response = await axios.post(`${BASE_URL}/api/conversations/create`, {
            friendlyName: 'Teste Unitário - ' + new Date().toLocaleString()
        });
        
        if (response.status === 200 && response.data.conversationSid) {
            logSuccess('Conversa criada com sucesso');
            log(`   SID: ${response.data.conversationSid}`);
            log(`   Nome: ${response.data.friendlyName}`);
            return response.data.conversationSid;
        } else {
            logError('Falha ao criar conversa');
            return null;
        }
    } catch (error) {
        logError(`Erro ao criar conversa: ${error.message}`);
        return null;
    }
}

async function testWhatsAppParticipant(conversationSid) {
    logTest('Adicionar participante WhatsApp');
    
    try {
        const response = await axios.post(`${BASE_URL}/api/conversations/${conversationSid}/participants/whatsapp`, {
            whatsappNumber: TEST_WHATSAPP_NUMBER
        });
        
        if (response.status === 200 && response.data.participantSid) {
            logSuccess('Participante WhatsApp adicionado');
            log(`   SID: ${response.data.participantSid}`);
            log(`   Address: ${response.data.address}`);
            log(`   Proxy: ${response.data.proxyAddress}`);
            return response.data.participantSid;
        } else {
            logError('Falha ao adicionar participante WhatsApp');
            return null;
        }
    } catch (error) {
        logError(`Erro ao adicionar participante WhatsApp: ${error.message}`);
        if (error.response?.data?.details) {
            log(`   Detalhes: ${error.response.data.details}`);
        }
        return null;
    }
}

async function testSendMessage(conversationSid) {
    logTest('Enviar mensagem de teste');
    
    try {
        const testMessage = `Teste unitário - ${new Date().toLocaleTimeString()}`;
        
        const response = await axios.post(`${BASE_URL}/api/conversations/${conversationSid}/messages`, {
            body: testMessage,
            author: 'test-user'
        });
        
        if (response.status === 200 && response.data.messageSid) {
            logSuccess('Mensagem enviada com sucesso');
            log(`   SID: ${response.data.messageSid}`);
            log(`   Conteúdo: ${response.data.body}`);
            return response.data.messageSid;
        } else {
            logError('Falha ao enviar mensagem');
            return null;
        }
    } catch (error) {
        logError(`Erro ao enviar mensagem: ${error.message}`);
        return null;
    }
}

async function testCleanup(conversationSid) {
    logTest('Limpar conversa de teste');
    
    try {
        const response = await axios.delete(`${BASE_URL}/api/conversations/${conversationSid}`);
        
        if (response.status === 200) {
            logSuccess('Conversa encerrada com sucesso');
            return true;
        } else {
            logError('Falha ao encerrar conversa');
            return false;
        }
    } catch (error) {
        logError(`Erro ao encerrar conversa: ${error.message}`);
        return false;
    }
}

async function runAllTests() {
    log('🚀 Iniciando testes do sistema WhatsApp Twilio\n', 'blue');
    
    const results = {
        total: 0,
        passed: 0,
        failed: 0
    };
    
    // Teste 1: Saúde do servidor
    results.total++;
    if (await testServerHealth()) {
        results.passed++;
    } else {
        results.failed++;
        log('\n❌ Teste crítico falhou. Parando execução.', 'red');
        return results;
    }
    
    // Teste 2: Webhooks
    results.total++;
    if (await testWebhooks()) {
        results.passed++;
    } else {
        results.failed++;
    }
    
    // Teste 3: Token
    results.total++;
    const token = await testTokenGeneration();
    if (token) {
        results.passed++;
    } else {
        results.failed++;
    }
    
    // Teste 4: WhatsApp
    results.total++;
    if (await testWhatsAppConnectivity()) {
        results.passed++;
    } else {
        results.failed++;
    }
    
    // Teste 5: Conversa
    results.total++;
    const conversationSid = await testConversationCreation();
    if (conversationSid) {
        results.passed++;
        
        // Teste 6: Participante WhatsApp
        results.total++;
        const participantSid = await testWhatsAppParticipant(conversationSid);
        if (participantSid) {
            results.passed++;
            
            // Teste 7: Enviar mensagem
            results.total++;
            if (await testSendMessage(conversationSid)) {
                results.passed++;
            } else {
                results.failed++;
            }
        } else {
            results.failed++;
        }
        
        // Limpeza
        await sleep(2000); // Aguardar 2 segundos
        await testCleanup(conversationSid);
        
    } else {
        results.failed++;
    }
    
    return results;
}

// Executar testes
runAllTests().then(results => {
    log('\n📊 Resultados dos testes:', 'blue');
    log(`   Total: ${results.total}`);
    log(`   Passou: ${results.passed}`, 'green');
    log(`   Falhou: ${results.failed}`, results.failed > 0 ? 'red' : 'green');
    
    const successRate = Math.round((results.passed / results.total) * 100);
    log(`   Taxa de sucesso: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
    
    if (results.failed === 0) {
        log('\n🎉 Todos os testes passaram! Sistema funcionando corretamente.', 'green');
        process.exit(0);
    } else {
        log('\n⚠️  Alguns testes falharam. Verifique a configuração.', 'yellow');
        process.exit(1);
    }
}).catch(error => {
    logError(`Erro durante execução dos testes: ${error.message}`);
    process.exit(1);
});
