#!/bin/bash

# Script para configurar ngrok para o sistema reativo
# Twilio WhatsApp Chat System

echo "🌐 Configurando ngrok para Sistema Reativo"
echo "=========================================="

# Verificar se ngrok está instalado
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok não encontrado!"
    echo "📥 Instale ngrok em: https://ngrok.com/download"
    echo "💡 Ou use: brew install ngrok (no Mac)"
    exit 1
fi

echo "✅ ngrok encontrado"

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "⚠️  Servidor não está rodando. Iniciando..."
    npm start &
    SERVER_PID=$!
    sleep 3
    
    if ! curl -s http://localhost:3000/health > /dev/null; then
        echo "❌ Falha ao iniciar servidor"
        exit 1
    fi
    echo "✅ Servidor iniciado"
else
    echo "✅ Servidor já está rodando"
    SERVER_PID=""
fi

# Verificar se já existe um túnel ngrok ativo
if pgrep -f "ngrok.*3000" > /dev/null; then
    echo "⚠️  Túnel ngrok já está ativo. Parando..."
    pkill -f "ngrok.*3000"
    sleep 2
fi

# Configurar ngrok com token se disponível
if [ ! -z "$NGROK_AUTH_TOKEN" ]; then
    echo "🔑 Configurando token ngrok..."
    ngrok config add-authtoken $NGROK_AUTH_TOKEN
fi

# Iniciar túnel ngrok
echo "🚀 Iniciando túnel ngrok..."
ngrok http 3000 --log=stdout > ngrok.log 2>&1 &
NGROK_PID=$!

# Aguardar ngrok inicializar
echo "⏳ Aguardando ngrok inicializar..."
sleep 5

# Obter URL pública do ngrok
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o '"public_url":"https://[^"]*"' | head -1 | cut -d'"' -f4)
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    echo "⏳ Tentativa $i/10 - Aguardando ngrok..."
    sleep 2
done

if [ -z "$NGROK_URL" ]; then
    echo "❌ Falha ao obter URL do ngrok"
    echo "🔍 Verifique os logs em ngrok.log"
    exit 1
fi

echo "✅ Túnel ngrok ativo: $NGROK_URL"

# Gerar URLs dos webhooks
WEBHOOK_INCOMING="$NGROK_URL/webhooks/whatsapp/incoming"
WEBHOOK_STATUS="$NGROK_URL/webhooks/whatsapp/status"
WEBHOOK_CONVERSATIONS="$NGROK_URL/webhooks/conversations"

# Salvar URLs em arquivo
cat > webhook-urls-reactive.txt << EOF
# URLs dos Webhooks para Sistema Reativo
# Gerado em: $(date)

Túnel ngrok: $NGROK_URL

## Para WhatsApp Sender (+18382700077):
Webhook URL: $WEBHOOK_INCOMING
Status callback URL: $WEBHOOK_STATUS

## Para Conversations Service:
Webhook URL: $WEBHOOK_CONVERSATIONS

## Configuração na Twilio Console:

### 1. WhatsApp Sender:
   - Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
   - Encontre: Sender +18382700077
   - Configure:
     * Webhook URL: $WEBHOOK_INCOMING
     * Status callback URL: $WEBHOOK_STATUS
     * HTTP Method: POST

### 2. Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione: Seu Conversations Service
   - Configure:
     * Webhook URL: $WEBHOOK_CONVERSATIONS
     * HTTP Method: POST
     * Events: onMessageAdded, onTypingStarted, onTypingEnded

## Teste:
curl $NGROK_URL/webhooks/test
EOF

echo ""
echo "📋 URLs dos Webhooks Geradas:"
echo "============================================"
echo "🌐 Túnel ngrok: $NGROK_URL"
echo ""
echo "📱 WhatsApp Sender Webhooks:"
echo "   Incoming: $WEBHOOK_INCOMING"
echo "   Status:   $WEBHOOK_STATUS"
echo ""
echo "💬 Conversations Webhook:"
echo "   Events:   $WEBHOOK_CONVERSATIONS"
echo ""
echo "📄 URLs salvas em: webhook-urls-reactive.txt"

# Testar webhooks
echo ""
echo "🧪 Testando webhooks..."
if curl -s "$NGROK_URL/webhooks/test" | grep -q "funcionando"; then
    echo "✅ Webhooks funcionando corretamente"
else
    echo "⚠️  Webhooks podem não estar funcionando"
fi

echo ""
echo "🎯 Próximos Passos:"
echo "=================="
echo "1. 📋 Configure os webhooks na Twilio Console usando as URLs acima"
echo "2. 🌐 Acesse: http://localhost:3000"
echo "3. 👨‍💼 Conecte-se como operador"
echo "4. 📱 Envie uma mensagem WhatsApp para +18382700077"
echo "5. 👀 Veja a conversa aparecer automaticamente na interface"
echo "6. 💬 Responda através da interface web"
echo ""
echo "🔧 Para parar o túnel ngrok:"
echo "   pkill -f 'ngrok.*3000'"
echo ""
echo "📊 Para monitorar logs:"
echo "   tail -f ngrok.log"
echo "   tail -f logs/twilio-$(date +%Y-%m-%d).log"

# Manter o script rodando para mostrar logs
echo ""
echo "🔄 Sistema reativo ativo! Pressione Ctrl+C para parar."
echo "📊 Monitorando logs do ngrok..."

# Função para limpar ao sair
cleanup() {
    echo ""
    echo "🛑 Parando sistema..."
    
    if [ ! -z "$NGROK_PID" ]; then
        kill $NGROK_PID 2>/dev/null || true
    fi
    
    pkill -f "ngrok.*3000" 2>/dev/null || true
    
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
    fi
    
    echo "✅ Sistema parado"
    exit 0
}

# Capturar Ctrl+C
trap cleanup SIGINT SIGTERM

# Mostrar logs do ngrok
tail -f ngrok.log 2>/dev/null || sleep infinity
