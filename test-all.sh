#!/bin/bash

# Script de teste completo para Mac
# Twilio WhatsApp Chat System

echo "🧪 Executando Testes do Sistema Reativo"
echo "======================================="

# Função para verificar se o servidor está rodando
check_server() {
    curl -s http://localhost:3000/health > /dev/null
    return $?
}

# Verificar se o servidor está rodando
if ! check_server; then
    echo "⚠️  Servidor não está rodando. Iniciando..."
    node server.js &
    SERVER_PID=$!
    sleep 3

    if ! check_server; then
        echo "❌ Falha ao iniciar servidor"
        exit 1
    fi
    echo "✅ Servidor iniciado"
else
    echo "✅ Servidor já está rodando"
    SERVER_PID=""
fi

echo ""

# Executar teste do sistema reativo
echo "🔄 Executando teste do sistema reativo..."
./test-reactive-system.sh
TEST_RESULT=$?

# Parar servidor se foi iniciado por este script
if [ ! -z "$SERVER_PID" ]; then
    echo "🛑 Parando servidor..."
    kill $SERVER_PID 2>/dev/null || true
fi

echo ""
echo "🎉 Testes do sistema reativo executados!"
echo "📊 Verifique os resultados acima para detalhes"

# Retornar o código de saída do teste reativo
exit $TEST_RESULT
