const express = require('express');
const twilio = require('twilio');
const logger = require('../utils/logger');
const router = express.Router();

// Inicializar cliente Twilio apenas se as credenciais estiverem disponíveis
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

/**
 * Buscar conversa existente com participante WhatsApp
 * GET /api/conversations/find-by-whatsapp/:whatsappNumber
 */
router.get('/find-by-whatsapp/:whatsappNumber', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber } = req.params;

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Buscando conversa existente para: ${formattedNumber}`);

        // Listar todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        // Para cada conversa, verificar se tem o participante WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Encontrar participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    // Encontrou conversa existente
                    return res.json({
                        found: true,
                        conversation: {
                            conversationSid: conversation.sid,
                            friendlyName: conversation.friendlyName,
                            uniqueName: conversation.uniqueName,
                            dateCreated: conversation.dateCreated,
                            dateUpdated: conversation.dateUpdated
                        },
                        whatsappParticipant: {
                            participantSid: whatsappParticipant.sid,
                            address: whatsappParticipant.messagingBinding?.address,
                            proxyAddress: whatsappParticipant.messagingBinding?.proxyAddress,
                            dateCreated: whatsappParticipant.dateCreated
                        },
                        participants: participants.map(p => ({
                            sid: p.sid,
                            identity: p.identity,
                            address: p.messagingBinding?.address,
                            type: p.messagingBinding ? 'whatsapp' : 'chat'
                        }))
                    });
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        // Não encontrou conversa existente
        res.json({
            found: false,
            message: 'Nenhuma conversa encontrada com este número WhatsApp',
            whatsappNumber: formattedNumber
        });

    } catch (error) {
        console.error('Erro ao buscar conversa existente:', error);
        res.status(500).json({
            error: 'Erro ao buscar conversa existente',
            details: error.message
        });
    }
});

/**
 * Conectar ou criar conversa com WhatsApp (inteligente)
 * POST /api/conversations/connect-whatsapp
 */
router.post('/connect-whatsapp', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber, friendlyName, userIdentity } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({
                error: 'Número do WhatsApp é obrigatório'
            });
        }

        if (!userIdentity) {
            return res.status(400).json({
                error: 'Identity do usuário é obrigatório'
            });
        }

        // Validar formato do número
        if (!whatsappNumber.match(/^\+\d{10,15}$/)) {
            return res.status(400).json({
                error: 'Formato do número WhatsApp inválido. Use o formato: +5511999999999'
            });
        }

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Conectando WhatsApp: ${formattedNumber} para usuário: ${userIdentity}`);

        // Primeiro, verificar se já existe uma conversa com este WhatsApp
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        let existingConversation = null;
        let whatsappParticipantExists = false;
        let chatParticipantExists = false;

        // Procurar conversa existente com o WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Verificar se tem o participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    existingConversation = conversation;
                    whatsappParticipantExists = true;

                    // Verificar se o usuário chat também já está na conversa
                    const chatParticipant = participants.find(p => p.identity === userIdentity);
                    if (chatParticipant) {
                        chatParticipantExists = true;
                    }
                    break;
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        let conversationData;
        let isNewConversation = false;

        if (existingConversation) {
            // Usar conversa existente
            console.log(`Usando conversa existente: ${existingConversation.sid}`);
            conversationData = {
                conversationSid: existingConversation.sid,
                friendlyName: existingConversation.friendlyName,
                uniqueName: existingConversation.uniqueName,
                dateCreated: existingConversation.dateCreated,
                dateUpdated: existingConversation.dateUpdated
            };
        } else {
            // Criar nova conversa
            console.log('Criando nova conversa...');
            isNewConversation = true;

            const conversationPayload = {
                friendlyName: friendlyName || `Conversa WhatsApp ${new Date().toLocaleString()}`
            };

            let newConversation;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                newConversation = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations
                    .create(conversationPayload);
            } else {
                newConversation = await client.conversations.v1
                    .conversations
                    .create(conversationPayload);
            }

            conversationData = {
                conversationSid: newConversation.sid,
                friendlyName: newConversation.friendlyName,
                uniqueName: newConversation.uniqueName,
                dateCreated: newConversation.dateCreated
            };
        }

        // Adicionar participante chat se não existir
        let chatParticipantData = null;
        if (!chatParticipantExists) {
            console.log(`Adicionando participante chat: ${userIdentity}`);
            try {
                let chatParticipant;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    chatParticipant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({ identity: userIdentity });
                } else {
                    chatParticipant = await client.conversations.v1
                        .conversations(conversationData.conversationSid)
                        .participants
                        .create({ identity: userIdentity });
                }

                chatParticipantData = {
                    participantSid: chatParticipant.sid,
                    identity: chatParticipant.identity,
                    dateCreated: chatParticipant.dateCreated
                };
            } catch (error) {
                if (error.message.includes('already exists') || error.message.includes('already a participant')) {
                    console.log(`Participante chat ${userIdentity} já existe na conversa`);
                    chatParticipantExists = true;
                } else {
                    throw error;
                }
            }
        } else {
            console.log(`Participante chat ${userIdentity} já existe na conversa`);
        }

        // Adicionar participante WhatsApp se não existir
        let whatsappParticipantData = null;
        if (!whatsappParticipantExists) {
            console.log(`Adicionando participante WhatsApp: ${formattedNumber}`);

            // Primeiro, limpar participante de outras conversas se existir
            try {
                console.log(`🧹 Limpando participante WhatsApp de outras conversas...`);

                // Listar todas as conversas para encontrar o participante
                let allConversations;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    allConversations = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations
                        .list({ limit: 100 });
                } else {
                    allConversations = await client.conversations.v1
                        .conversations
                        .list({ limit: 100 });
                }

                // Procurar e remover participante de outras conversas
                for (const conv of allConversations) {
                    if (conv.sid === conversationData.conversationSid) continue; // Pular conversa atual

                    try {
                        let participants;
                        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                            participants = await client.conversations.v1
                                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                .conversations(conv.sid)
                                .participants
                                .list();
                        } else {
                            participants = await client.conversations.v1
                                .conversations(conv.sid)
                                .participants
                                .list();
                        }

                        const existingParticipant = participants.find(p =>
                            p.messagingBinding && p.messagingBinding.address === formattedNumber
                        );

                        if (existingParticipant) {
                            console.log(`🗑️ Removendo participante de conversa ${conv.sid}`);

                            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                                await client.conversations.v1
                                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                    .conversations(conv.sid)
                                    .participants(existingParticipant.sid)
                                    .remove();
                            } else {
                                await client.conversations.v1
                                    .conversations(conv.sid)
                                    .participants(existingParticipant.sid)
                                    .remove();
                            }

                            console.log(`✅ Participante removido de ${conv.sid}`);
                        }
                    } catch (cleanupError) {
                        console.warn(`⚠️ Erro ao limpar conversa ${conv.sid}: ${cleanupError.message}`);
                    }
                }
            } catch (cleanupError) {
                console.warn(`⚠️ Erro na limpeza geral: ${cleanupError.message}`);
            }

            try {
                // Função para criar participante WhatsApp - CORRIGIDA
                async function createParticipantWithFallback(conversationSid, formattedNumber) {
                    // Usar apenas o número registrado que está funcionando
                    const proxy = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';

                    console.log(`🔧 Criando participante WhatsApp:`);
                    console.log(`   Address: ${formattedNumber}`);
                    console.log(`   Proxy: ${proxy}`);
                    console.log(`   Conversation: ${conversationSid}`);

                    try {
                        let participant;
                        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                            participant = await client.conversations.v1
                                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                .conversations(conversationSid)
                                .participants
                                .create({
                                    'messagingBinding.address': formattedNumber,
                                    'messagingBinding.proxyAddress': proxy
                                });
                        } else {
                            participant = await client.conversations.v1
                                .conversations(conversationSid)
                                .participants
                                .create({
                                    'messagingBinding.address': formattedNumber,
                                    'messagingBinding.proxyAddress': proxy
                                });
                        }

                        console.log(`✅ Participante WhatsApp criado:`);
                        console.log(`   SID: ${participant.sid}`);
                        console.log(`   Address: ${participant.messagingBinding?.address}`);
                        console.log(`   Proxy: ${participant.messagingBinding?.proxyAddress}`);
                        console.log(`   Type: ${participant.messagingBinding?.type}`);

                        return participant;

                    } catch (error) {
                        console.log(`❌ Erro ao criar participante: ${error.message}`);
                        console.log(`❌ Código do erro: ${error.code}`);

                        // Se participante já existe, buscar o existente
                        if (error.code === 50416 || error.message.includes('already exists')) {
                            console.log(`🔍 Participante já existe, buscando existente...`);

                            try {
                                let participants;
                                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                                    participants = await client.conversations.v1
                                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                        .conversations(conversationSid)
                                        .participants
                                        .list();
                                } else {
                                    participants = await client.conversations.v1
                                        .conversations(conversationSid)
                                        .participants
                                        .list();
                                }

                                const existingParticipant = participants.find(p =>
                                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                                );

                                if (existingParticipant) {
                                    console.log(`✅ Participante existente encontrado: ${existingParticipant.sid}`);
                                    return existingParticipant;
                                }
                            } catch (searchError) {
                                console.log(`⚠️ Erro ao buscar participante existente: ${searchError.message}`);
                            }
                        }

                        throw error;
                    }
                }

                // Criar participante com fallback
                const whatsappParticipant = await createParticipantWithFallback(conversationData.conversationSid, formattedNumber);

                whatsappParticipantData = {
                    participantSid: whatsappParticipant.sid,
                    address: whatsappParticipant.messagingBinding?.address,
                    proxyAddress: whatsappParticipant.messagingBinding?.proxyAddress,
                    dateCreated: whatsappParticipant.dateCreated
                };
            } catch (error) {
                if (error.message.includes('already exists') || error.message.includes('already a participant')) {
                    console.log(`Participante WhatsApp ${formattedNumber} já existe na conversa`);
                    whatsappParticipantExists = true;
                } else {
                    throw error;
                }
            }
        } else {
            console.log(`Participante WhatsApp ${formattedNumber} já existe na conversa`);
        }

        // Resposta de sucesso
        res.json({
            success: true,
            conversation: conversationData,
            isNewConversation,
            participants: {
                chat: {
                    exists: chatParticipantExists,
                    data: chatParticipantData,
                    identity: userIdentity
                },
                whatsapp: {
                    exists: whatsappParticipantExists,
                    data: whatsappParticipantData,
                    address: formattedNumber
                }
            },
            message: existingConversation
                ? `Conectado à conversa existente: ${existingConversation.friendlyName}`
                : `Nova conversa criada: ${conversationData.friendlyName}`
        });

    } catch (error) {
        console.error('Erro ao conectar conversa WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao conectar conversa WhatsApp',
            details: error.message
        });
    }
});

/**
 * Criar uma nova conversa
 * POST /api/conversations/create
 */
router.post('/create', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { friendlyName, uniqueName } = req.body;
        
        const conversationData = {
            friendlyName: friendlyName || `Conversa ${new Date().toLocaleString()}`,
        };

        if (uniqueName) {
            conversationData.uniqueName = uniqueName;
        }

        // Criar conversa usando o service configurado ou o padrão
        let conversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .create(conversationData);
        } else {
            conversation = await client.conversations.v1
                .conversations
                .create(conversationData);
        }

        res.json({
            conversationSid: conversation.sid,
            friendlyName: conversation.friendlyName,
            uniqueName: conversation.uniqueName,
            dateCreated: conversation.dateCreated
        });

    } catch (error) {
        console.error('Erro ao criar conversa:', error);
        res.status(500).json({ 
            error: 'Erro ao criar conversa',
            details: error.message 
        });
    }
});

/**
 * Adicionar participante WhatsApp à conversa
 * POST /api/conversations/:conversationSid/participants/whatsapp
 */
router.post('/:conversationSid/participants/whatsapp', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { conversationSid } = req.params;
        const { whatsappNumber } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({
                error: 'Número do WhatsApp é obrigatório'
            });
        }

        // Validar formato do número
        if (!whatsappNumber.match(/^\+\d{10,15}$/)) {
            return res.status(400).json({
                error: 'Formato do número WhatsApp inválido. Use o formato: +5511999999999'
            });
        }

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        // Função para criar participante WhatsApp - CORRIGIDA
        async function createParticipantWithFallback(conversationSid, formattedNumber) {
            // Usar apenas o número registrado que está funcionando
            const proxy = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';

            console.log(`🔧 Criando participante WhatsApp:`);
            console.log(`   Address: ${formattedNumber}`);
            console.log(`   Proxy: ${proxy}`);
            console.log(`   Conversation: ${conversationSid}`);

            try {
                let participant;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': proxy
                        });
                } else {
                    participant = await client.conversations.v1
                        .conversations(conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': proxy
                        });
                }

                console.log(`✅ Participante WhatsApp criado:`);
                console.log(`   SID: ${participant.sid}`);
                console.log(`   Address: ${participant.messagingBinding?.address}`);
                console.log(`   Proxy: ${participant.messagingBinding?.proxyAddress}`);
                console.log(`   Type: ${participant.messagingBinding?.type}`);

                return participant;

            } catch (error) {
                console.log(`❌ Erro ao criar participante: ${error.message}`);
                console.log(`❌ Código do erro: ${error.code}`);

                // Se participante já existe, buscar o existente
                if (error.code === 50416 || error.message.includes('already exists')) {
                    console.log(`🔍 Participante já existe, buscando existente...`);

                    try {
                        let participants;
                        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                            participants = await client.conversations.v1
                                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                .conversations(conversationSid)
                                .participants
                                .list();
                        } else {
                            participants = await client.conversations.v1
                                .conversations(conversationSid)
                                .participants
                                .list();
                        }

                        const existingParticipant = participants.find(p =>
                            p.messagingBinding && p.messagingBinding.address === formattedNumber
                        );

                        if (existingParticipant) {
                            console.log(`✅ Participante existente encontrado: ${existingParticipant.sid}`);
                            return existingParticipant;
                        }
                    } catch (searchError) {
                        console.log(`⚠️ Erro ao buscar participante existente: ${searchError.message}`);
                    }
                }

                throw error;
            }
        }

        // Criar participante com fallback
        const participant = await createParticipantWithFallback(conversationSid, formattedNumber);

        res.json({
            participantSid: participant.sid,
            address: participant.messagingBinding?.address,
            proxyAddress: participant.messagingBinding?.proxyAddress,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante WhatsApp:', error);

        // Tratar erro específico de participante já existente
        if (error.message.includes('already exists')) {
            res.status(409).json({
                error: 'Participante WhatsApp já existe em outra conversa',
                details: 'Este número WhatsApp já está sendo usado em outra conversa. Use um número diferente ou remova-o da conversa anterior.',
                suggestion: 'Tente usar um número WhatsApp diferente'
            });
        } else {
            res.status(500).json({
                error: 'Erro ao adicionar participante WhatsApp',
                details: error.message
            });
        }
    }
});

/**
 * Adicionar participante chat à conversa
 * POST /api/conversations/:conversationSid/participants/chat
 */
router.post('/:conversationSid/participants/chat', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { conversationSid } = req.params;
        const { identity } = req.body;

        if (!identity) {
            return res.status(400).json({
                error: 'Identity é obrigatório'
            });
        }

        // Adicionar participante
        let participant;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participant = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        } else {
            participant = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .create({ identity: identity });
        }

        res.json({
            participantSid: participant.sid,
            identity: participant.identity,
            dateCreated: participant.dateCreated
        });

    } catch (error) {
        console.error('Erro ao adicionar participante chat:', error);
        res.status(500).json({ 
            error: 'Erro ao adicionar participante chat',
            details: error.message 
        });
    }
});

/**
 * Enviar mensagem para a conversa
 * POST /api/conversations/:conversationSid/messages
 */
router.post('/:conversationSid/messages', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { body, author } = req.body;

        if (!body) {
            return res.status(400).json({
                error: 'Conteúdo da mensagem é obrigatório'
            });
        }

        console.log(`📤 Enviando mensagem para conversa ${conversationSid}:`, body);

        // 1. Primeiro, buscar informações da conversa para ver se é WhatsApp
        let conversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .fetch();
        } else {
            conversation = await client.conversations.v1
                .conversations(conversationSid)
                .fetch();
        }

        // 2. Verificar se é conversa WhatsApp pelos atributos ou nome
        let isWhatsAppConversation = false;
        let whatsappNumber = null;

        try {
            const attributes = JSON.parse(conversation.attributes || '{}');
            whatsappNumber = attributes.whatsappNumber;
            isWhatsAppConversation = !!whatsappNumber;
        } catch (e) {
            // Se não tem atributos, verificar pelo nome da conversa
            if (conversation.friendlyName && conversation.friendlyName.includes('WhatsApp')) {
                isWhatsAppConversation = true;
                // Extrair número do nome da conversa
                const match = conversation.friendlyName.match(/\+\d+/);
                if (match) {
                    whatsappNumber = `whatsapp:${match[0]}`;
                }
            }
        }

        // 3. Se é conversa WhatsApp, enviar via API direta
        if (isWhatsAppConversation && whatsappNumber) {
            console.log(`📱 Enviando mensagem WhatsApp direta para: ${whatsappNumber}`);

            const fromNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';

            const whatsappMessage = await client.messages.create({
                body: body,
                from: fromNumber,
                to: whatsappNumber
            });

            console.log(`✅ Mensagem WhatsApp enviada: ${whatsappMessage.sid}`);

            // Também adicionar à conversa para histórico
            const messageData = { body };
            if (author) {
                messageData.author = author;
            }

            let conversationMessage;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                conversationMessage = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(conversationSid)
                    .messages
                    .create(messageData);
            } else {
                conversationMessage = await client.conversations.v1
                    .conversations(conversationSid)
                    .messages
                    .create(messageData);
            }

            // Log da mensagem enviada
            logger.messageSent({
                conversationSid: conversationSid,
                messageSid: whatsappMessage.sid,
                conversationMessageSid: conversationMessage.sid,
                body: body,
                author: author || 'system',
                to: whatsappNumber,
                method: 'whatsapp_direct'
            });

            res.json({
                messageSid: whatsappMessage.sid,
                conversationMessageSid: conversationMessage.sid,
                body: body,
                author: author || 'system',
                dateCreated: conversationMessage.dateCreated,
                method: 'whatsapp_direct',
                to: whatsappNumber
            });

        } else {
            // 4. Se não é WhatsApp, usar Conversations normal
            console.log(`💬 Enviando mensagem via Conversations`);

            const messageData = { body };
            if (author) {
                messageData.author = author;
            }

            let message;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                message = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(conversationSid)
                    .messages
                    .create(messageData);
            } else {
                message = await client.conversations.v1
                    .conversations(conversationSid)
                    .messages
                    .create(messageData);
            }

            console.log(`✅ Mensagem enviada: ${message.sid}`);

            // Log da mensagem enviada
            logger.messageSent({
                conversationSid: conversationSid,
                messageSid: message.sid,
                body: message.body,
                author: message.author,
                to: 'conversation',
                method: 'conversations'
            });

            res.json({
                messageSid: message.sid,
                body: message.body,
                author: message.author,
                dateCreated: message.dateCreated,
                method: 'conversations'
            });
        }

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        res.status(500).json({
            error: 'Erro ao enviar mensagem',
            details: error.message
        });
    }
});

/**
 * Limpar participantes WhatsApp de todas as conversas (para testes)
 * DELETE /api/conversations/cleanup-whatsapp/:whatsappNumber
 */
router.delete('/cleanup-whatsapp/:whatsappNumber', async (req, res) => {
    try {
        // Verificar se o cliente está inicializado
        if (!client) {
            return res.status(500).json({
                error: 'Cliente Twilio não inicializado. Verifique as credenciais no arquivo .env'
            });
        }

        const { whatsappNumber } = req.params;

        // Formatar número do WhatsApp
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber
            : `whatsapp:${whatsappNumber}`;

        console.log(`Limpando participante WhatsApp: ${formattedNumber}`);

        // Listar todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 100 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 100 });
        }

        let removedCount = 0;

        // Para cada conversa, verificar se tem o participante WhatsApp
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Encontrar participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    // Remover participante
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    } else {
                        await client.conversations.v1
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    }
                    removedCount++;
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        res.json({
            message: `Participante WhatsApp removido de ${removedCount} conversa(s)`,
            whatsappNumber: formattedNumber,
            conversationsProcessed: conversations.length,
            participantsRemoved: removedCount
        });

    } catch (error) {
        console.error('Erro ao limpar participante WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao limpar participante WhatsApp',
            details: error.message
        });
    }
});

/**
 * Listar conversas
 * GET /api/conversations
 */
router.get('/', async (req, res) => {
    try {
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ state: 'active', limit: 20 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ state: 'active', limit: 20 });
        }

        const conversationList = conversations.map(conv => ({
            sid: conv.sid,
            friendlyName: conv.friendlyName,
            uniqueName: conv.uniqueName,
            dateCreated: conv.dateCreated,
            dateUpdated: conv.dateUpdated
        }));

        res.json({ conversations: conversationList });

    } catch (error) {
        console.error('Erro ao listar conversas:', error);
        res.status(500).json({ 
            error: 'Erro ao listar conversas',
            details: error.message 
        });
    }
});

/**
 * Encerrar conversa individual
 * DELETE /api/conversations/:sid
 */
router.delete('/:sid', async (req, res) => {
    try {
        const { sid } = req.params;

        console.log(`🔚 Encerrando conversa: ${sid}`);

        // Encerrar conversa
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(sid)
                .update({ state: 'closed' });
        } else {
            await client.conversations.v1
                .conversations(sid)
                .update({ state: 'closed' });
        }

        console.log(`✅ Conversa ${sid} encerrada com sucesso`);

        res.json({
            message: 'Conversa encerrada com sucesso',
            conversationSid: sid,
            state: 'closed'
        });

    } catch (error) {
        console.error('Erro ao encerrar conversa:', error);

        // Se a conversa já está fechada, considerar como sucesso
        if (error.code === 50377) {
            res.json({
                message: 'Conversa já estava encerrada',
                conversationSid: sid,
                state: 'closed'
            });
        } else {
            res.status(500).json({
                error: 'Erro ao encerrar conversa',
                details: error.message
            });
        }
    }
});

/**
 * Encerrar todas as conversas ativas
 * DELETE /api/conversations/close-all
 */
router.delete('/close-all/execute', async (req, res) => {
    try {
        console.log(`🔚 Encerrando todas as conversas ativas...`);

        // Listar todas as conversas ativas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ state: 'active', limit: 100 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ state: 'active', limit: 100 });
        }

        console.log(`📋 Encontradas ${conversations.length} conversas ativas`);

        let closedCount = 0;
        let errors = [];

        // Encerrar cada conversa
        for (const conversation of conversations) {
            try {
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .update({ state: 'closed' });
                } else {
                    await client.conversations.v1
                        .conversations(conversation.sid)
                        .update({ state: 'closed' });
                }

                console.log(`✅ Conversa ${conversation.sid} encerrada`);
                closedCount++;

            } catch (error) {
                console.error(`❌ Erro ao encerrar conversa ${conversation.sid}:`, error.message);
                errors.push({
                    conversationSid: conversation.sid,
                    error: error.message
                });
            }
        }

        console.log(`🎉 ${closedCount} conversas encerradas com sucesso`);
        if (errors.length > 0) {
            console.log(`⚠️ ${errors.length} erros encontrados`);
        }

        res.json({
            message: `${closedCount} conversas encerradas com sucesso`,
            totalFound: conversations.length,
            successCount: closedCount,
            errorCount: errors.length,
            errors: errors
        });

    } catch (error) {
        console.error('Erro ao encerrar todas as conversas:', error);
        res.status(500).json({
            error: 'Erro ao encerrar todas as conversas',
            details: error.message
        });
    }
});

/**
 * Limpar participante WhatsApp de todas as conversas (para correção)
 * DELETE /api/conversations/cleanup-whatsapp/:number
 */
router.delete('/cleanup-whatsapp/:number', async (req, res) => {
    try {
        const { number } = req.params;
        const formattedNumber = number.startsWith('whatsapp:') ? number : `whatsapp:${number}`;

        console.log(`🧹 Limpando participante WhatsApp: ${formattedNumber}`);

        // Listar todas as conversas
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        let removedCount = 0;

        for (const conversation of conversations) {
            try {
                // Listar participantes da conversa
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                // Encontrar participante WhatsApp
                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    console.log(`🗑️ Removendo participante ${formattedNumber} da conversa ${conversation.sid}`);

                    // Remover participante
                    if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                        await client.conversations.v1
                            .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    } else {
                        await client.conversations.v1
                            .conversations(conversation.sid)
                            .participants(whatsappParticipant.sid)
                            .remove();
                    }

                    removedCount++;
                }
            } catch (error) {
                console.warn(`Erro ao processar conversa ${conversation.sid}:`, error.message);
            }
        }

        res.json({
            message: `Participante WhatsApp removido de ${removedCount} conversa(s)`,
            number: formattedNumber,
            removedCount
        });

    } catch (error) {
        console.error('Erro ao limpar participante WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao limpar participante WhatsApp',
            details: error.message
        });
    }
});


/**
 * Enviar mensagem direta via WhatsApp (alternativa quando Conversations falha)
 * POST /api/conversations/send-direct-whatsapp
 */
router.post('/send-direct-whatsapp', async (req, res) => {
    try {
        const { to, body, from } = req.body;

        if (!to || !body) {
            return res.status(400).json({
                error: 'Destinatário (to) e mensagem (body) são obrigatórios'
            });
        }

        // Formatar números
        const toNumber = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`;
        // Usar apenas o número registrado que está funcionando
        const fromNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';

        console.log(`🔧 Enviando mensagem direta WhatsApp:`);
        console.log(`   De: ${fromNumber}`);
        console.log(`   Para: ${toNumber}`);
        console.log(`   Mensagem: ${body}`);

        const message = await client.messages.create({
            body: body,
            from: fromNumber,
            to: toNumber
        });

        console.log(`✅ Mensagem enviada com sucesso!`);
        console.log(`   Message SID: ${message.sid}`);
        console.log(`   Status: ${message.status}`);

        // Log da mensagem enviada
        logger.messageSent({
            messageSid: message.sid,
            from: fromNumber,
            to: toNumber,
            body: body,
            method: 'direct_whatsapp'
        });

        res.json({
            success: true,
            messageSid: message.sid,
            from: fromNumber,
            to: toNumber,
            body: body,
            status: message.status,
            method: 'direct_whatsapp'
        });

    } catch (error) {
        console.error('Erro ao enviar mensagem direta WhatsApp:', error);
        res.status(500).json({
            error: 'Erro ao enviar mensagem direta WhatsApp',
            details: error.message,
            code: error.code
        });
    }
});

/**
 * Testar conectividade WhatsApp
 * GET /api/conversations/test-whatsapp
 */
router.get('/test-whatsapp', async (req, res) => {
    try {
        const testNumber = req.query.number || '+5551993590210';
        // Garantir formato correto - remover espaços e formatar adequadamente
        let cleanNumber = testNumber.replace(/\s+/g, ''); // Remove espaços

        if (cleanNumber.startsWith('whatsapp:')) {
            const phoneNumber = cleanNumber.replace('whatsapp:', '');
            if (!phoneNumber.startsWith('+')) {
                cleanNumber = `whatsapp:+${phoneNumber}`;
            }
        } else {
            if (!cleanNumber.startsWith('+')) {
                cleanNumber = `+${cleanNumber}`;
            }
            cleanNumber = `whatsapp:${cleanNumber}`;
        }

        const formattedNumber = cleanNumber;

        console.log(`🔍 Debug formatação:`);
        console.log(`   Número original: "${testNumber}"`);
        console.log(`   Número limpo: "${cleanNumber}"`);
        console.log(`   Número formatado: "${formattedNumber}"`);
        console.log(`🧪 Testando conectividade WhatsApp para: ${formattedNumber}`);

        // Testar apenas com o número registrado
        const senders = [
            {
                name: 'Número Registrado',
                number: process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077'
            }
        ];

        const results = [];

        for (const sender of senders) {
            try {
                const message = await client.messages.create({
                    body: `🧪 Teste de conectividade WhatsApp via ${sender.name} - ${new Date().toLocaleString()}`,
                    from: sender.number,
                    to: formattedNumber
                });

                results.push({
                    sender: sender.name,
                    number: sender.number,
                    success: true,
                    messageSid: message.sid,
                    status: message.status
                });

                console.log(`✅ Teste com ${sender.name} bem-sucedido: ${message.sid}`);

            } catch (error) {
                results.push({
                    sender: sender.name,
                    number: sender.number,
                    success: false,
                    error: error.message,
                    code: error.code
                });

                console.log(`❌ Teste com ${sender.name} falhou: ${error.message}`);
            }
        }

        const successCount = results.filter(r => r.success).length;

        res.json({
            testNumber: formattedNumber,
            results: results,
            summary: {
                total: senders.length,
                successful: successCount,
                failed: senders.length - successCount
            },
            recommendation: successCount > 0 ? 'Sistema funcionando' : 'Verificar configuração'
        });

    } catch (error) {
        console.error('Erro no teste WhatsApp:', error);
        res.status(500).json({
            error: 'Erro no teste WhatsApp',
            details: error.message
        });
    }
});


/**
 * Limpeza robusta de participantes órfãos
 * POST /api/conversations/cleanup-orphaned/:whatsappNumber
 */
router.post('/cleanup-orphaned/:whatsappNumber', async (req, res) => {
    try {
        const whatsappNumber = decodeURIComponent(req.params.whatsappNumber);

        console.log(`🧹 Iniciando limpeza robusta para: ${whatsappNumber}`);

        let removedCount = 0;

        // 1. Buscar TODAS as conversas (incluindo inativas)
        let allConversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            allConversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 100 });
        } else {
            allConversations = await client.conversations.v1
                .conversations
                .list({ limit: 100 });
        }

        console.log(`🔍 Verificando ${allConversations.length} conversas...`);

        // 2. Verificar cada conversa
        for (const conv of allConversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conv.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conv.sid)
                        .participants
                        .list();
                }

                // 3. Encontrar participante com o número WhatsApp
                const targetParticipant = participants.find(p =>
                    p.messagingBinding &&
                    p.messagingBinding.address === whatsappNumber
                );

                if (targetParticipant) {
                    console.log(`🗑️ Removendo participante ${targetParticipant.sid} da conversa ${conv.sid} (${conv.state})`);

                    try {
                        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                            await client.conversations.v1
                                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                .conversations(conv.sid)
                                .participants(targetParticipant.sid)
                                .remove();
                        } else {
                            await client.conversations.v1
                                .conversations(conv.sid)
                                .participants(targetParticipant.sid)
                                .remove();
                        }

                        removedCount++;
                        console.log(`✅ Participante removido da conversa ${conv.sid}`);

                    } catch (removeError) {
                        console.log(`⚠️ Erro ao remover participante da conversa ${conv.sid}: ${removeError.message}`);

                        // Se a conversa não existe mais, tentar deletá-la
                        if (removeError.code === 20404) {
                            console.log(`🗑️ Conversa ${conv.sid} não existe mais, ignorando...`);
                        }
                    }
                }

            } catch (participantError) {
                console.log(`⚠️ Erro ao verificar participantes da conversa ${conv.sid}: ${participantError.message}`);

                // Se a conversa não existe mais, ignorar
                if (participantError.code === 20404) {
                    console.log(`🗑️ Conversa ${conv.sid} não existe mais, ignorando...`);
                }
            }
        }

        console.log(`🧹 Limpeza concluída: ${removedCount} participante(s) removido(s)`);

        res.json({
            success: true,
            message: `Limpeza concluída: ${removedCount} participante(s) removido(s)`,
            whatsappNumber: whatsappNumber,
            removedCount: removedCount
        });

    } catch (error) {
        console.error(`❌ Erro na limpeza robusta: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message,
            details: error.stack
        });
    }
});

/**
 * Atualizar atributos de uma conversa (para marcar como WhatsApp)
 * POST /api/conversations/:conversationSid/update-attributes
 */
router.post('/:conversationSid/update-attributes', async (req, res) => {
    try {
        const { conversationSid } = req.params;
        const { whatsappNumber } = req.body;

        if (!whatsappNumber) {
            return res.status(400).json({
                error: 'whatsappNumber é obrigatório'
            });
        }

        console.log(`🔧 Atualizando atributos da conversa ${conversationSid} com WhatsApp: ${whatsappNumber}`);

        const attributes = JSON.stringify({ whatsappNumber });

        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .update({ attributes });
        } else {
            await client.conversations.v1
                .conversations(conversationSid)
                .update({ attributes });
        }

        console.log(`✅ Atributos atualizados para conversa ${conversationSid}`);

        res.json({
            success: true,
            conversationSid: conversationSid,
            attributes: { whatsappNumber }
        });

    } catch (error) {
        console.error('Erro ao atualizar atributos:', error);
        res.status(500).json({
            error: 'Erro ao atualizar atributos',
            details: error.message
        });
    }
});

module.exports = router;
