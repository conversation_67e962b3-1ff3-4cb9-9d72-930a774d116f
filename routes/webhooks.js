const express = require('express');
const twilio = require('twilio');
const logger = require('../utils/logger');
const router = express.Router();

// Inicializar cliente Twilio
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

// Middleware para validar assinatura do Twilio (opcional, mas recomendado)
const validateTwilioSignature = (req, res, next) => {
    // Em desenvolvimento local, pular validação
    if (process.env.NODE_ENV === 'development') {
        return next();
    }

    const twilioSignature = req.headers['x-twilio-signature'];
    const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
    
    if (!twilioSignature) {
        logger.error({
            message: 'Webhook sem assinatura Twilio',
            context: { url, headers: req.headers }
        });
        return res.status(403).send('Forbidden');
    }

    const isValid = twilio.validateRequest(
        process.env.TWILIO_AUTH_TOKEN,
        twilioSignature,
        url,
        req.body
    );

    if (!isValid) {
        logger.error({
            message: 'Assinatura Twilio inválida',
            context: { url, signature: twilioSignature }
        });
        return res.status(403).send('Forbidden');
    }

    next();
};

/**
 * Limpeza robusta de participantes órfãos
 */
async function cleanupOrphanedParticipant(whatsappNumber) {
    console.log(`🧹 Iniciando limpeza robusta para: ${whatsappNumber}`);

    try {
        let removedCount = 0;

        // 1. Buscar TODAS as conversas (incluindo inativas)
        let allConversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            allConversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 100 });
        } else {
            allConversations = await client.conversations.v1
                .conversations
                .list({ limit: 100 });
        }

        console.log(`🔍 Verificando ${allConversations.length} conversas...`);

        // 2. Verificar cada conversa
        for (const conv of allConversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conv.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conv.sid)
                        .participants
                        .list();
                }

                // 3. Encontrar participante com o número WhatsApp
                const targetParticipant = participants.find(p =>
                    p.messagingBinding &&
                    p.messagingBinding.address === whatsappNumber
                );

                if (targetParticipant) {
                    console.log(`🗑️ Removendo participante ${targetParticipant.sid} da conversa ${conv.sid} (${conv.state})`);

                    try {
                        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                            await client.conversations.v1
                                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                                .conversations(conv.sid)
                                .participants(targetParticipant.sid)
                                .remove();
                        } else {
                            await client.conversations.v1
                                .conversations(conv.sid)
                                .participants(targetParticipant.sid)
                                .remove();
                        }

                        removedCount++;
                        console.log(`✅ Participante removido da conversa ${conv.sid}`);

                    } catch (removeError) {
                        console.log(`⚠️ Erro ao remover participante da conversa ${conv.sid}: ${removeError.message}`);

                        // Se a conversa não existe mais, tentar deletá-la
                        if (removeError.code === 20404) {
                            console.log(`🗑️ Conversa ${conv.sid} não existe mais, ignorando...`);
                        }
                    }
                }

            } catch (participantError) {
                console.log(`⚠️ Erro ao verificar participantes da conversa ${conv.sid}: ${participantError.message}`);

                // Se a conversa não existe mais, ignorar
                if (participantError.code === 20404) {
                    console.log(`🗑️ Conversa ${conv.sid} não existe mais, ignorando...`);
                }
            }
        }

        console.log(`🧹 Limpeza concluída: ${removedCount} participante(s) removido(s)`);

        return {
            success: true,
            removedCount: removedCount
        };

    } catch (error) {
        console.error(`❌ Erro na limpeza robusta: ${error.message}`);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Encontrar conversa existente ou criar nova para o número WhatsApp
 */
async function findOrCreateConversation(whatsappNumber, messageBody, messageSid) {
    if (!client) {
        throw new Error('Cliente Twilio não inicializado');
    }

    try {
        // Garantir formato correto do número - CORRIGIDO
        let cleanNumber = whatsappNumber.replace(/\s+/g, ''); // Remove todos os espaços

        console.log(`🔍 Número original: "${whatsappNumber}"`);
        console.log(`🔍 Número sem espaços: "${cleanNumber}"`);

        // Se já tem whatsapp:, garantir que tem o +
        if (cleanNumber.startsWith('whatsapp:')) {
            const phoneNumber = cleanNumber.replace('whatsapp:', '');
            if (!phoneNumber.startsWith('+')) {
                cleanNumber = `whatsapp:+${phoneNumber}`;
            }
        } else {
            // Se não tem whatsapp:, adicionar com +
            if (!cleanNumber.startsWith('+')) {
                cleanNumber = `+${cleanNumber}`;
            }
            cleanNumber = `whatsapp:${cleanNumber}`;
        }

        const formattedNumber = cleanNumber;
        console.log(`🔍 Número formatado final: "${formattedNumber}"`);

        // 1. Buscar conversas existentes por atributos (mais eficiente)
        console.log(`🔍 Buscando conversas existentes para: ${formattedNumber}`);

        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        // Buscar conversa que contenha o número no nome (mais simples e eficiente)
        let existingConversation = null;
        const phoneNumber = formattedNumber.replace('whatsapp:', '');

        for (const conv of conversations) {
            if (conv.state === 'active' && conv.friendlyName && conv.friendlyName.includes(phoneNumber)) {
                existingConversation = conv;
                console.log(`✅ Conversa existente encontrada: ${conv.sid} - ${conv.friendlyName}`);
                break;
            }
        }

        // 2. Se encontrou conversa existente, adicionar mensagem a ela
        if (existingConversation) {
            console.log(`📝 Adicionando mensagem à conversa existente: ${existingConversation.sid}`);

            try {
                let message;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    message = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(existingConversation.sid)
                        .messages
                        .create({
                            body: messageBody,
                            author: formattedNumber,
                            xTwilioWebhookEnabled: 'true'
                        });
                } else {
                    message = await client.conversations.v1
                        .conversations(existingConversation.sid)
                        .messages
                        .create({
                            body: messageBody,
                            author: formattedNumber,
                            xTwilioWebhookEnabled: 'true'
                        });
                }

                console.log(`✅ Mensagem adicionada à conversa: ${message.sid}`);

                logger.writeLog('message_added_to_existing', {
                    conversationSid: existingConversation.sid,
                    messageSid: message.sid,
                    originalMessageSid: messageSid,
                    whatsappNumber: formattedNumber,
                    body: messageBody
                });

                return existingConversation;

            } catch (messageError) {
                console.error(`❌ Erro ao adicionar mensagem à conversa existente: ${messageError.message}`);
                // Continuar para criar nova conversa se falhar
            }
        }

        // 3. Se não encontrou conversa existente, criar nova
        console.log(`📝 Criando nova conversa para: ${formattedNumber}`);
        return await createAutomaticConversation(formattedNumber, messageBody, messageSid);

    } catch (error) {
        console.error('❌ Erro em findOrCreateConversation:', error);
        throw error;
    }
}

/**
 * Criar conversa automaticamente quando receber mensagem de usuário
 */
async function createAutomaticConversation(whatsappNumber, firstMessage, messageSid) {
    if (!client) {
        throw new Error('Cliente Twilio não inicializado');
    }

    try {
        // Garantir formato correto do número - CORRIGIDO
        let cleanNumber = whatsappNumber.replace(/\s+/g, ''); // Remove todos os espaços

        console.log(`🔍 Número original: "${whatsappNumber}"`);
        console.log(`🔍 Número sem espaços: "${cleanNumber}"`);

        // Se já tem whatsapp:, garantir que tem o +
        if (cleanNumber.startsWith('whatsapp:')) {
            const phoneNumber = cleanNumber.replace('whatsapp:', '');
            if (!phoneNumber.startsWith('+')) {
                cleanNumber = `whatsapp:+${phoneNumber}`;
            }
        } else {
            // Se não tem whatsapp:, adicionar com +
            if (!cleanNumber.startsWith('+')) {
                cleanNumber = `+${cleanNumber}`;
            }
            cleanNumber = `whatsapp:${cleanNumber}`;
        }

        const formattedNumber = cleanNumber;

        console.log(`🔍 Número formatado final: "${formattedNumber}"`);

        // Verificar se já existe uma conversa com este WhatsApp
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        // Verificar se já existe conversa com este número
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    console.log(`✅ Conversa existente encontrada: ${conversation.sid}`);
                    return conversation;
                }
            } catch (error) {
                console.warn(`Erro ao verificar conversa ${conversation.sid}:`, error.message);
            }
        }

        // Criar nova conversa
        const phoneNumber = formattedNumber.replace('whatsapp:', '');
        const friendlyName = `Chat WhatsApp ${phoneNumber} - ${new Date().toLocaleString()}`;

        console.log(`📝 Criando nova conversa: ${friendlyName}`);

        let newConversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            newConversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .create({ friendlyName });
        } else {
            newConversation = await client.conversations.v1
                .conversations
                .create({ friendlyName });
        }

        // Função para criar participante com fallback - CORRIGIDA
        async function createParticipantWithFallback(conversationSid, formattedNumber) {
            // Usar apenas o número registrado que está funcionando
            const proxy = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+18382700077';

            console.log(`🔧 Criando participante WhatsApp:`);
            console.log(`   Address: ${formattedNumber}`);
            console.log(`   Proxy: ${proxy}`);
            console.log(`   Conversation: ${conversationSid}`);

            try {
                let participant;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participant = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': proxy
                        });
                } else {
                    participant = await client.conversations.v1
                        .conversations(conversationSid)
                        .participants
                        .create({
                            'messagingBinding.address': formattedNumber,
                            'messagingBinding.proxyAddress': proxy
                        });
                }

                console.log(`✅ Participante WhatsApp criado:`);
                console.log(`   SID: ${participant.sid}`);
                console.log(`   Address: ${participant.messagingBinding?.address}`);
                console.log(`   Proxy: ${participant.messagingBinding?.proxyAddress}`);
                console.log(`   Type: ${participant.messagingBinding?.type}`);

                return participant;

            } catch (error) {
                console.log(`❌ Erro ao criar participante: ${error.message}`);
                console.log(`❌ Código do erro: ${error.code}`);

                // Se participante já existe, criar um participante "dummy" para continuar
                if (error.code === 50416 || error.message.includes('already exists')) {
                    console.log(`⚠️ Participante já existe em outra conversa, criando participante dummy...`);

                    // Retornar um objeto participante dummy para não quebrar o fluxo
                    return {
                        sid: 'dummy-participant',
                        messagingBinding: {
                            address: formattedNumber,
                            proxyAddress: proxy,
                            type: 'whatsapp'
                        },
                        isDummy: true
                    };
                }

                throw error;
            }
        }

        // NÃO criar participante WhatsApp - isso causa problemas
        // O WhatsApp será tratado via API direta de mensagens
        console.log(`📱 Conversa criada para WhatsApp: ${formattedNumber} (sem participante Conversations)`);

        // Salvar o número WhatsApp nos atributos da conversa para referência
        try {
            const attributes = JSON.stringify({ whatsappNumber: formattedNumber });
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(newConversation.sid)
                    .update({ attributes });
            } else {
                await client.conversations.v1
                    .conversations(newConversation.sid)
                    .update({ attributes });
            }
            console.log(`✅ Número WhatsApp salvo nos atributos da conversa`);
        } catch (attrError) {
            console.log(`⚠️ Erro ao salvar atributos: ${attrError.message}`);
        }

        // Adicionar participante operador padrão para que as mensagens sejam visíveis
        console.log(`👨‍💼 Adicionando operador padrão à conversa...`);
        try {
            const defaultOperator = process.env.DEFAULT_USER_IDENTITY || 'web-user';

            let operatorParticipant;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                operatorParticipant = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(newConversation.sid)
                    .participants
                    .create({ identity: defaultOperator });
            } else {
                operatorParticipant = await client.conversations.v1
                    .conversations(newConversation.sid)
                    .participants
                    .create({ identity: defaultOperator });
            }

            console.log(`✅ Operador ${defaultOperator} adicionado: ${operatorParticipant.sid}`);

        } catch (operatorError) {
            if (operatorError.message.includes('already exists')) {
                console.log(`✅ Operador já existe na conversa`);
            } else {
                console.log(`⚠️ Erro ao adicionar operador: ${operatorError.message}`);
            }
        }

        // Adicionar a mensagem recebida à conversa
        console.log(`📝 Adicionando mensagem recebida à nova conversa...`);
        try {
            let message;
            if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                message = await client.conversations.v1
                    .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                    .conversations(newConversation.sid)
                    .messages
                    .create({
                        body: firstMessage,
                        author: formattedNumber,
                        xTwilioWebhookEnabled: 'true'
                    });
            } else {
                message = await client.conversations.v1
                    .conversations(newConversation.sid)
                    .messages
                    .create({
                        body: firstMessage,
                        author: formattedNumber,
                        xTwilioWebhookEnabled: 'true'
                    });
            }

            console.log(`✅ Mensagem adicionada à nova conversa: ${message.sid}`);

        } catch (messageError) {
            console.error(`⚠️ Erro ao adicionar mensagem à nova conversa: ${messageError.message}`);
        }

        console.log(`🎉 Conversa criada automaticamente: ${newConversation.sid}`);

        logger.writeLog('conversation_created', {
            conversationSid: newConversation.sid,
            friendlyName: friendlyName,
            whatsappNumber: formattedNumber,
            firstMessage: firstMessage,
            trigger: 'incoming_message',
            originalMessageSid: messageSid,
            approach: 'hybrid_whatsapp'
        });

        return newConversation;

    } catch (error) {
        console.error('Erro ao criar conversa automática:', error);
        throw error;
    }
}

/**
 * Webhook para mensagens recebidas do WhatsApp
 * POST /webhooks/whatsapp/incoming
 */
router.post('/whatsapp/incoming', validateTwilioSignature, async (req, res) => {
    try {
        logger.webhookReceived(req);
        logger.messageReceived(req.body);

        const {
            MessageSid,
            From,
            To,
            Body,
            ConversationSid,
            ParticipantSid,
            Author
        } = req.body;

        console.log('📱 Mensagem WhatsApp recebida:', {
            from: From,
            to: To,
            body: Body,
            conversationSid: ConversationSid,
            messageSid: MessageSid
        });

        // SEMPRE processar mensagens diretas do WhatsApp
        if (From && (From.includes('whatsapp:') || From.includes('whatsapp '))) {
            console.log('🔄 Processando mensagem WhatsApp de:', From);

            try {
                // Primeiro, tentar encontrar conversa existente para este número
                const conversation = await findOrCreateConversation(From, Body, MessageSid);
                console.log(`✅ Conversa processada: ${conversation.sid}`);

                // Log da mensagem processada
                logger.writeLog('message_processed', {
                    from: From,
                    to: To,
                    body: Body,
                    messageSid: MessageSid,
                    conversationSid: conversation.sid,
                    processed: true
                });

            } catch (error) {
                console.error('❌ Erro ao processar mensagem WhatsApp:', error);
                logger.error({
                    message: 'Erro ao processar mensagem WhatsApp',
                    stack: error.stack,
                    context: { from: From, to: To, body: Body, messageSid: MessageSid }
                });
            }
        } else {
            console.log('⚠️ Mensagem não é do WhatsApp ou formato inválido:', From);
        }

        // Resposta TwiML vazia para não enviar resposta automática
        const twiml = new twilio.twiml.MessagingResponse();

        res.type('text/xml');
        res.send(twiml.toString());

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de mensagem WhatsApp',
            stack: error.stack,
            context: { body: req.body }
        });

        console.error('Erro no webhook WhatsApp:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Webhook para status de mensagens
 * POST /webhooks/whatsapp/status
 */
router.post('/whatsapp/status', validateTwilioSignature, (req, res) => {
    try {
        logger.webhookReceived(req);

        const {
            MessageSid,
            MessageStatus,
            To,
            From,
            ErrorCode,
            ErrorMessage
        } = req.body;

        console.log('📊 Status de mensagem WhatsApp:', {
            messageSid: MessageSid,
            status: MessageStatus,
            to: To,
            from: From,
            error: ErrorCode ? { code: ErrorCode, message: ErrorMessage } : null
        });

        logger.writeLog('message_status', {
            messageSid: MessageSid,
            status: MessageStatus,
            to: To,
            from: From,
            errorCode: ErrorCode,
            errorMessage: ErrorMessage
        });

        res.status(200).send('OK');

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de status WhatsApp',
            stack: error.stack,
            context: { body: req.body }
        });
        
        console.error('Erro no webhook de status:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Webhook para eventos de Conversations
 * POST /webhooks/conversations
 */
router.post('/conversations', validateTwilioSignature, (req, res) => {
    try {
        logger.webhookReceived(req);
        logger.conversationEvent(req.body);

        const {
            EventType,
            ConversationSid,
            ParticipantSid,
            Author,
            Body,
            MessageSid
        } = req.body;

        console.log('💬 Evento de Conversations:', {
            eventType: EventType,
            conversationSid: ConversationSid,
            author: Author,
            body: Body
        });

        // Tratar diferentes tipos de eventos
        switch (EventType) {
            case 'onMessageAdded':
                console.log('📝 Nova mensagem na conversa:', Body);
                break;
            case 'onTypingStarted':
                console.log('⌨️ Usuário começou a digitar:', Author);
                logger.typingIndicator({
                    conversationSid: ConversationSid,
                    participantSid: ParticipantSid,
                    participant: Author,
                    action: 'started'
                });
                break;
            case 'onTypingEnded':
                console.log('⌨️ Usuário parou de digitar:', Author);
                logger.typingIndicator({
                    conversationSid: ConversationSid,
                    participantSid: ParticipantSid,
                    participant: Author,
                    action: 'ended'
                });
                break;
            case 'onParticipantJoined':
                console.log('👤 Participante entrou:', Author);
                break;
            case 'onParticipantLeft':
                console.log('👤 Participante saiu:', Author);
                break;
        }

        res.status(200).send('OK');

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de Conversations',
            stack: error.stack,
            context: { body: req.body }
        });
        
        console.error('Erro no webhook Conversations:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Endpoint para testar webhooks
 * GET /webhooks/test
 */
router.get('/test', (req, res) => {
    res.json({
        message: 'Webhooks funcionando!',
        timestamp: new Date().toISOString(),
        endpoints: {
            whatsapp_incoming: '/webhooks/whatsapp/incoming',
            whatsapp_status: '/webhooks/whatsapp/status',
            conversations: '/webhooks/conversations'
        }
    });
});

/**
 * Endpoint para ver logs recentes
 * GET /webhooks/logs
 */
router.get('/logs', (req, res) => {
    try {
        const { type = 'twilio', hours = 24 } = req.query;
        const logs = logger.getRecentLogs(type, parseInt(hours));
        
        res.json({
            logs,
            count: logs.length,
            type,
            hours: parseInt(hours)
        });
    } catch (error) {
        logger.error({
            message: 'Erro ao buscar logs',
            stack: error.stack
        });
        
        res.status(500).json({ error: 'Erro ao buscar logs' });
    }
});

module.exports = router;
