const express = require('express');
const twilio = require('twilio');
const logger = require('../utils/logger');
const router = express.Router();

// Inicializar cliente Twilio
let client = null;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

// Middleware para validar assinatura do Twilio (opcional, mas recomendado)
const validateTwilioSignature = (req, res, next) => {
    // Em desenvolvimento local, pular validação
    if (process.env.NODE_ENV === 'development') {
        return next();
    }

    const twilioSignature = req.headers['x-twilio-signature'];
    const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
    
    if (!twilioSignature) {
        logger.error({
            message: 'Webhook sem assinatura Twilio',
            context: { url, headers: req.headers }
        });
        return res.status(403).send('Forbidden');
    }

    const isValid = twilio.validateRequest(
        process.env.TWILIO_AUTH_TOKEN,
        twilioSignature,
        url,
        req.body
    );

    if (!isValid) {
        logger.error({
            message: 'Assinatura Twilio inválida',
            context: { url, signature: twilioSignature }
        });
        return res.status(403).send('Forbidden');
    }

    next();
};

/**
 * Criar conversa automaticamente quando receber mensagem de usuário
 */
async function createAutomaticConversation(whatsappNumber, firstMessage) {
    if (!client) {
        throw new Error('Cliente Twilio não inicializado');
    }

    try {
        // Garantir formato correto do número
        const formattedNumber = whatsappNumber.startsWith('whatsapp:')
            ? whatsappNumber.replace(/\s+/g, '') // Remove espaços
            : `whatsapp:${whatsappNumber.replace(/\s+/g, '')}`;

        console.log(`🔍 Número formatado: ${formattedNumber}`);

        // Verificar se já existe uma conversa com este WhatsApp
        let conversations;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversations = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .list({ limit: 50 });
        } else {
            conversations = await client.conversations.v1
                .conversations
                .list({ limit: 50 });
        }

        // Verificar se já existe conversa com este número
        for (const conversation of conversations) {
            try {
                let participants;
                if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
                    participants = await client.conversations.v1
                        .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                        .conversations(conversation.sid)
                        .participants
                        .list();
                } else {
                    participants = await client.conversations.v1
                        .conversations(conversation.sid)
                        .participants
                        .list();
                }

                const whatsappParticipant = participants.find(p =>
                    p.messagingBinding && p.messagingBinding.address === formattedNumber
                );

                if (whatsappParticipant) {
                    console.log(`✅ Conversa existente encontrada: ${conversation.sid}`);
                    return conversation;
                }
            } catch (error) {
                console.warn(`Erro ao verificar conversa ${conversation.sid}:`, error.message);
            }
        }

        // Criar nova conversa
        const phoneNumber = formattedNumber.replace('whatsapp:', '');
        const friendlyName = `Chat WhatsApp ${phoneNumber} - ${new Date().toLocaleString()}`;

        console.log(`📝 Criando nova conversa: ${friendlyName}`);

        let newConversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            newConversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations
                .create({ friendlyName });
        } else {
            newConversation = await client.conversations.v1
                .conversations
                .create({ friendlyName });
        }

        // Adicionar participante WhatsApp - usar WhatsApp Sender registrado
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER || 'whatsapp:+14155238886';

        console.log(`👤 Adicionando participante WhatsApp: ${formattedNumber}`);
        console.log(`📱 Usando WhatsApp Sender: ${senderNumber}`);

        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(newConversation.sid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        } else {
            await client.conversations.v1
                .conversations(newConversation.sid)
                .participants
                .create({
                    'messagingBinding.address': formattedNumber,
                    'messagingBinding.proxyAddress': senderNumber
                });
        }

        console.log(`🎉 Conversa criada automaticamente: ${newConversation.sid}`);

        logger.writeLog('conversation_created', {
            conversationSid: newConversation.sid,
            friendlyName: friendlyName,
            whatsappNumber: formattedNumber,
            firstMessage: firstMessage,
            trigger: 'incoming_message'
        });

        return newConversation;

    } catch (error) {
        console.error('Erro ao criar conversa automática:', error);
        throw error;
    }
}

/**
 * Webhook para mensagens recebidas do WhatsApp
 * POST /webhooks/whatsapp/incoming
 */
router.post('/whatsapp/incoming', validateTwilioSignature, async (req, res) => {
    try {
        logger.webhookReceived(req);
        logger.messageReceived(req.body);

        const {
            MessageSid,
            From,
            To,
            Body,
            ConversationSid,
            ParticipantSid,
            Author
        } = req.body;

        console.log('📱 Mensagem WhatsApp recebida:', {
            from: From,
            to: To,
            body: Body,
            conversationSid: ConversationSid
        });

        // Se não há ConversationSid, significa que é uma mensagem direta
        // Precisamos criar uma conversa automaticamente
        if (!ConversationSid && From && From.startsWith('whatsapp:')) {
            console.log('🔄 Criando conversa automática para:', From);

            try {
                await createAutomaticConversation(From, Body);
            } catch (error) {
                console.error('Erro ao criar conversa automática:', error);
                logger.error({
                    message: 'Erro ao criar conversa automática',
                    stack: error.stack,
                    context: { from: From, body: Body }
                });
            }
        }

        // Resposta TwiML vazia para não enviar resposta automática
        const twiml = new twilio.twiml.MessagingResponse();

        res.type('text/xml');
        res.send(twiml.toString());

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de mensagem WhatsApp',
            stack: error.stack,
            context: { body: req.body }
        });

        console.error('Erro no webhook WhatsApp:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Webhook para status de mensagens
 * POST /webhooks/whatsapp/status
 */
router.post('/whatsapp/status', validateTwilioSignature, (req, res) => {
    try {
        logger.webhookReceived(req);

        const {
            MessageSid,
            MessageStatus,
            To,
            From,
            ErrorCode,
            ErrorMessage
        } = req.body;

        console.log('📊 Status de mensagem WhatsApp:', {
            messageSid: MessageSid,
            status: MessageStatus,
            to: To,
            from: From,
            error: ErrorCode ? { code: ErrorCode, message: ErrorMessage } : null
        });

        logger.writeLog('message_status', {
            messageSid: MessageSid,
            status: MessageStatus,
            to: To,
            from: From,
            errorCode: ErrorCode,
            errorMessage: ErrorMessage
        });

        res.status(200).send('OK');

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de status WhatsApp',
            stack: error.stack,
            context: { body: req.body }
        });
        
        console.error('Erro no webhook de status:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Webhook para eventos de Conversations
 * POST /webhooks/conversations
 */
router.post('/conversations', validateTwilioSignature, (req, res) => {
    try {
        logger.webhookReceived(req);
        logger.conversationEvent(req.body);

        const {
            EventType,
            ConversationSid,
            ParticipantSid,
            Author,
            Body,
            MessageSid
        } = req.body;

        console.log('💬 Evento de Conversations:', {
            eventType: EventType,
            conversationSid: ConversationSid,
            author: Author,
            body: Body
        });

        // Tratar diferentes tipos de eventos
        switch (EventType) {
            case 'onMessageAdded':
                console.log('📝 Nova mensagem na conversa:', Body);
                break;
            case 'onTypingStarted':
                console.log('⌨️ Usuário começou a digitar:', Author);
                logger.typingIndicator({
                    conversationSid: ConversationSid,
                    participantSid: ParticipantSid,
                    participant: Author,
                    action: 'started'
                });
                break;
            case 'onTypingEnded':
                console.log('⌨️ Usuário parou de digitar:', Author);
                logger.typingIndicator({
                    conversationSid: ConversationSid,
                    participantSid: ParticipantSid,
                    participant: Author,
                    action: 'ended'
                });
                break;
            case 'onParticipantJoined':
                console.log('👤 Participante entrou:', Author);
                break;
            case 'onParticipantLeft':
                console.log('👤 Participante saiu:', Author);
                break;
        }

        res.status(200).send('OK');

    } catch (error) {
        logger.error({
            message: 'Erro no webhook de Conversations',
            stack: error.stack,
            context: { body: req.body }
        });
        
        console.error('Erro no webhook Conversations:', error);
        res.status(500).send('Internal Server Error');
    }
});

/**
 * Endpoint para testar webhooks
 * GET /webhooks/test
 */
router.get('/test', (req, res) => {
    res.json({
        message: 'Webhooks funcionando!',
        timestamp: new Date().toISOString(),
        endpoints: {
            whatsapp_incoming: '/webhooks/whatsapp/incoming',
            whatsapp_status: '/webhooks/whatsapp/status',
            conversations: '/webhooks/conversations'
        }
    });
});

/**
 * Endpoint para ver logs recentes
 * GET /webhooks/logs
 */
router.get('/logs', (req, res) => {
    try {
        const { type = 'twilio', hours = 24 } = req.query;
        const logs = logger.getRecentLogs(type, parseInt(hours));
        
        res.json({
            logs,
            count: logs.length,
            type,
            hours: parseInt(hours)
        });
    } catch (error) {
        logger.error({
            message: 'Erro ao buscar logs',
            stack: error.stack
        });
        
        res.status(500).json({ error: 'Erro ao buscar logs' });
    }
});

module.exports = router;
