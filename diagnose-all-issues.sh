#!/bin/bash

# Script de diagnóstico completo para todos os problemas
# Verifica servidor, webhooks, conversas e mensagens

echo "🔍 DIAGNÓSTICO COMPLETO - Todos os Problemas"
echo "============================================"

echo ""
echo "📋 1. Verificando Status do Servidor"
echo "===================================="

# Verificar se servidor está rodando
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Servidor está rodando na porta 3000"
else
    echo "❌ Servidor não está respondendo"
    echo "🔧 Reiniciando servidor..."
    # Matar processos na porta 3000
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
    # Iniciar servidor em background
    cd /Users/<USER>/Documents/augment-projects/twiliowhats
    npm start > server.log 2>&1 &
    sleep 5
    
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ Servidor reiniciado com sucesso"
    else
        echo "❌ Falha ao reiniciar servidor"
        echo "📋 Logs do servidor:"
        tail -10 server.log 2>/dev/null || echo "Nenhum log encontrado"
        exit 1
    fi
fi

echo ""
echo "📋 2. Testando APIs Básicas"
echo "=========================="

# Testar autenticação
echo "🔐 Testando autenticação Twilio..."
AUTH_TEST=$(curl -s http://localhost:3000/api/auth/verify)
if echo "$AUTH_TEST" | grep -q '"status":"OK"'; then
    echo "✅ Autenticação Twilio: OK"
else
    echo "❌ Autenticação Twilio: FALHA"
    echo "📋 Resposta: $AUTH_TEST"
fi

# Testar listagem de conversas
echo ""
echo "💬 Testando listagem de conversas..."
CONV_TEST=$(curl -s http://localhost:3000/api/conversations)
if echo "$CONV_TEST" | grep -q '"conversations"'; then
    CONV_COUNT=$(echo "$CONV_TEST" | jq '.conversations | length' 2>/dev/null || echo "0")
    echo "✅ API Conversas: OK ($CONV_COUNT conversas ativas)"
    
    if [ "$CONV_COUNT" -gt 0 ]; then
        echo "📋 Conversas encontradas:"
        echo "$CONV_TEST" | jq '.conversations[] | {sid: .sid, name: .friendlyName}' 2>/dev/null || echo "Erro ao parsear conversas"
    fi
else
    echo "❌ API Conversas: FALHA"
    echo "📋 Resposta: $CONV_TEST"
fi

# Testar webhooks
echo ""
echo "🔗 Testando webhooks..."
WEBHOOK_TEST=$(curl -s http://localhost:3000/webhooks/test)
if echo "$WEBHOOK_TEST" | grep -q "funcionando"; then
    echo "✅ Webhooks: OK"
else
    echo "❌ Webhooks: FALHA"
    echo "📋 Resposta: $WEBHOOK_TEST"
fi

echo ""
echo "📋 3. Testando Funcionalidades de Encerramento"
echo "=============================================="

# Criar conversa de teste
echo "📝 Criando conversa de teste..."
CREATE_TEST=$(curl -s -X POST "http://localhost:3000/api/conversations/connect-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{"whatsappNumber":"+5551999999999","userIdentity":"teste-diagnostico"}')

if echo "$CREATE_TEST" | grep -q '"conversationSid"'; then
    TEST_CONV_SID=$(echo "$CREATE_TEST" | jq -r '.conversationSid' 2>/dev/null)
    echo "✅ Conversa de teste criada: $TEST_CONV_SID"
    
    # Testar encerramento individual
    echo ""
    echo "🔚 Testando encerramento individual..."
    CLOSE_TEST=$(curl -s -X DELETE "http://localhost:3000/api/conversations/$TEST_CONV_SID")
    
    if echo "$CLOSE_TEST" | grep -q '"message"'; then
        echo "✅ Encerramento individual: OK"
        echo "📋 Resposta: $(echo "$CLOSE_TEST" | jq -r '.message' 2>/dev/null)"
    else
        echo "❌ Encerramento individual: FALHA"
        echo "📋 Resposta: $CLOSE_TEST"
    fi
    
else
    echo "❌ Falha ao criar conversa de teste"
    echo "📋 Resposta: $CREATE_TEST"
fi

# Testar encerramento de todas
echo ""
echo "🔚 Testando encerramento de todas as conversas..."
CLOSE_ALL_TEST=$(curl -s -X DELETE "http://localhost:3000/api/conversations/close-all/execute")

if echo "$CLOSE_ALL_TEST" | grep -q '"message"'; then
    CLOSED_COUNT=$(echo "$CLOSE_ALL_TEST" | jq -r '.successCount' 2>/dev/null)
    ERROR_COUNT=$(echo "$CLOSE_ALL_TEST" | jq -r '.errorCount' 2>/dev/null)
    echo "✅ Encerramento de todas: OK"
    echo "📋 Conversas encerradas: $CLOSED_COUNT"
    echo "📋 Erros: $ERROR_COUNT"
else
    echo "❌ Encerramento de todas: FALHA"
    echo "📋 Resposta: $CLOSE_ALL_TEST"
fi

echo ""
echo "📋 4. Testando Webhooks WhatsApp"
echo "==============================="

# Simular webhook de mensagem recebida
echo "📱 Simulando webhook de mensagem WhatsApp..."
WEBHOOK_SIM=$(curl -s -X POST "http://localhost:3000/whatsapp/incoming" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+*************&To=whatsapp:+***********&Body=Teste%20diagnostico&MessageSid=TEST_DIAG_$(date +%s)&AccountSid=TEST&ApiVersion=2010-04-01")

echo "📤 Resposta do webhook: $WEBHOOK_SIM"

# Verificar se criou conversa automática
sleep 3
echo ""
echo "🔍 Verificando se conversa automática foi criada..."
CONV_AFTER=$(curl -s http://localhost:3000/api/conversations)
CONV_COUNT_AFTER=$(echo "$CONV_AFTER" | jq '.conversations | length' 2>/dev/null || echo "0")
echo "📋 Conversas após webhook: $CONV_COUNT_AFTER"

echo ""
echo "📋 5. Verificando Logs"
echo "===================="

echo "📄 Últimos logs do sistema:"
if [ -f "logs/twilio-$(date +%Y-%m-%d).log" ]; then
    echo "📋 Últimas 5 entradas dos logs:"
    tail -5 "logs/twilio-$(date +%Y-%m-%d).log" | while read line; do
        echo "   $line"
    done
else
    echo "❌ Arquivo de log não encontrado"
fi

echo ""
echo "📋 6. Testando Criação de Participante"
echo "====================================="

# Criar nova conversa para teste de participante
echo "📝 Criando conversa para teste de participante..."
PART_CONV=$(curl -s -X POST "http://localhost:3000/api/conversations/connect-whatsapp" \
  -H "Content-Type: application/json" \
  -d '{"whatsappNumber":"+5551888888888","userIdentity":"teste-participante"}')

if echo "$PART_CONV" | grep -q '"conversationSid"'; then
    PART_CONV_SID=$(echo "$PART_CONV" | jq -r '.conversationSid' 2>/dev/null)
    PROXY_ADDR=$(echo "$PART_CONV" | jq -r '.proxyAddress' 2>/dev/null)
    
    echo "✅ Conversa criada: $PART_CONV_SID"
    echo "📱 Proxy Address: $PROXY_ADDR"
    
    if [ "$PROXY_ADDR" != "null" ] && [ "$PROXY_ADDR" != "" ]; then
        echo "🎉 PROXY ADDRESS FUNCIONANDO!"
        
        # Testar envio de mensagem
        echo ""
        echo "💬 Testando envio de mensagem..."
        MSG_TEST=$(curl -s -X POST "http://localhost:3000/api/conversations/$PART_CONV_SID/messages" \
          -H "Content-Type: application/json" \
          -d '{"body":"Teste de diagnóstico - mensagem deve funcionar!","author":"diagnostico"}')
        
        if echo "$MSG_TEST" | grep -q '"messageSid"'; then
            MSG_SID=$(echo "$MSG_TEST" | jq -r '.messageSid' 2>/dev/null)
            echo "✅ Mensagem enviada: $MSG_SID"
            echo "🎯 SISTEMA FUNCIONANDO!"
        else
            echo "❌ Falha ao enviar mensagem"
            echo "📋 Resposta: $MSG_TEST"
        fi
        
    else
        echo "❌ Proxy Address não definido"
    fi
    
    # Limpar conversa de teste
    curl -s -X DELETE "http://localhost:3000/api/conversations/$PART_CONV_SID" > /dev/null
    
else
    echo "❌ Falha ao criar conversa para teste"
    echo "📋 Resposta: $PART_CONV"
fi

echo ""
echo "📋 RESUMO DO DIAGNÓSTICO"
echo "======================="

echo "🔍 Problemas identificados:"
echo "1. Verificar se webhooks estão recebendo mensagens"
echo "2. Verificar se proxy address está sendo definido"
echo "3. Verificar se logs estão sendo gerados"

echo ""
echo "🔧 Próximas ações:"
echo "1. Verificar configuração do ngrok"
echo "2. Testar envio manual de mensagem WhatsApp"
echo "3. Verificar logs em tempo real"

echo ""
echo "🌐 Interface disponível em: http://localhost:3000"
echo "📊 Para monitorar logs: tail -f logs/twilio-$(date +%Y-%m-%d).log"
