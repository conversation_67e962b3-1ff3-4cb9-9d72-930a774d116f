#!/bin/bash

# Script para iniciar ngrok com configuração automática
# Uso: ./start-ngrok.sh

echo "🚀 Iniciando ngrok para WhatsApp Twilio..."

# Verificar se ngrok está instalado
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok não encontrado. Instale com: brew install ngrok"
    exit 1
fi

# Verificar se o arquivo .env existe
if [ ! -f .env ]; then
    echo "❌ Arquivo .env não encontrado"
    exit 1
fi

# Carregar variáveis do .env
source .env

# Verificar se o token está configurado
if [ -z "$NGROK_AUTH_TOKEN" ]; then
    echo "❌ NGROK_AUTH_TOKEN não configurado no .env"
    exit 1
fi

# Configurar token do ngrok
echo "🔑 Configurando token do ngrok..."
ngrok config add-authtoken $NGROK_AUTH_TOKEN

# Porta do servidor (padrão 3000)
PORT=${PORT:-3000}

echo "🌐 Iniciando túnel ngrok na porta $PORT..."
echo "📱 Webhooks estarão disponíveis em:"
echo "   - Mensagens recebidas: https://[URL]/webhooks/whatsapp/incoming"
echo "   - Status de mensagens: https://[URL]/webhooks/whatsapp/status"
echo "   - Eventos de conversas: https://[URL]/webhooks/conversations"
echo ""
echo "⚠️  IMPORTANTE: Configure estes URLs no console da Twilio"
echo "💡 Pressione Ctrl+C para parar o ngrok"
echo ""

# Iniciar ngrok
ngrok http $PORT
