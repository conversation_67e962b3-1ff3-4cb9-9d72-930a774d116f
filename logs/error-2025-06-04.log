{"timestamp":"2025-06-04T16:54:19.448Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Invalid messaging binding address\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:124:13)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:192:17","context":{"from":"whatsapp: 5551993590210","body":"Teste de correção"}}
{"timestamp":"2025-06-04T17:52:18.915Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Invalid messaging binding address\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:133:13)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:201:17","context":{"from":"whatsapp: 5551993590210","body":"Teste webhook corrigido"}}
{"timestamp":"2025-06-04T17:55:14.823Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Invalid messaging binding address\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:133:13)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:201:17","context":{"from":"whatsapp: 5551888888888","body":"Teste webhook final"}}
{"timestamp":"2025-06-04T18:05:32.445Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Falha ao criar participante com todos os proxies disponíveis\n    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:207:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:212:9)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:264:17","context":{"from":"whatsapp: 5551993590210","body":"Oi, preciso de ajuda"}}
{"timestamp":"2025-06-04T18:12:24.477Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Falha ao criar participante com todos os proxies disponíveis\n    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:207:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:212:9)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:264:17","context":{"from":"whatsapp: 5551993590210","body":"Teste real de webhook"}}
{"timestamp":"2025-06-04T18:15:42.444Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Falha ao criar participante com todos os proxies disponíveis\n    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17","context":{"from":"whatsapp: 5551888888888","body":"Teste unitario 2 webhook"}}
{"timestamp":"2025-06-04T18:36:16.150Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Falha ao criar participante com todos os proxies disponíveis\n    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17","context":{"from":"whatsapp:+555193590210","body":"Oi"}}
{"timestamp":"2025-06-04T18:37:13.761Z","type":"error","message":"Erro ao criar conversa automática","stack":"Error: Falha ao criar participante com todos os proxies disponíveis\n    at createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:221:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:226:9)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:278:17","context":{"from":"whatsapp:+555193590210","body":"Oiii"}}
{"timestamp":"2025-06-04T18:57:25.000Z","type":"error","message":"Erro ao processar mensagem WhatsApp","stack":"Error: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:155:35)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:223:37)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:306:38","context":{"from":"whatsapp: 5551993590210","to":"whatsapp: 18382700077","body":"Oi, preciso de ajuda","messageSid":"TEST_WEBHOOK_1749063433"}}
{"timestamp":"2025-06-04T19:36:24.260Z","type":"error","message":"Erro ao processar mensagem WhatsApp","stack":"Error: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:155:35)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:223:37)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:306:38","context":{"from":"whatsapp:+555193590210","to":"whatsapp:+18382700077","body":"Oi","messageSid":"SMde6dcc865a911a3e05319957e02b7f6e"}}
{"timestamp":"2025-06-04T19:44:42.078Z","type":"error","message":"Erro ao processar mensagem WhatsApp","stack":"Error: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:155:35)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:223:37)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:306:38","context":{"from":"whatsapp:+555193590210","to":"whatsapp:+18382700077","body":"Oi 2 a partir do celular","messageSid":"SM126692edc8808bd3b71c90bc36c751fc"}}
{"timestamp":"2025-06-04T19:50:41.236Z","type":"error","message":"Erro ao processar mensagem WhatsApp","stack":"Error: A binding for this participant and proxy address already exists in Conversation CH8470dcbd63f742d7814c7907bc71e37e\n    at success (/Users/<USER>/Documents/augment-projects/twiliowhats/node_modules/twilio/lib/base/Version.js:79:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createParticipantWithFallback (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:292:35)\n    at async createAutomaticConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:400:37)\n    at async findOrCreateConversation (/Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:177:16)\n    at async /Users/<USER>/Documents/augment-projects/twiliowhats/routes/webhooks.js:515:38","context":{"from":"whatsapp:+555193590210","to":"whatsapp:+18382700077","body":"Oi do celular","messageSid":"SM23618364f43db95265f517f17932aa43"}}
