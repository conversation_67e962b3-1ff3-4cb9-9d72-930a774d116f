#!/bin/bash

# Script para analisar as mensagens que funcionaram
# Descobrir exatamente qual configuração permitiu o envio

echo "🔍 ANÁLISE - Mensagens que Funcionaram"
echo "======================================"

# Configurações
CONVERSATION_SID="CH329dd7f817714f1389025c01ddc6e248"
MESSAGE_1="IM0b2f4502d5144ba480f68588303203ab"  # "sistema corrigido"
MESSAGE_2="IMd2240095868c4b5ab9b5643649c1a38e"  # "API PADRÃO"

echo "💬 Conversa analisada: $CONVERSATION_SID"
echo "📝 Mensagem 1: $MESSAGE_1 (sistema corrigido)"
echo "📝 Mensagem 2: $MESSAGE_2 (API PADRÃO)"

echo ""
echo "🔍 Analisando participantes da conversa que funcionou..."

# Criar script Node.js para analisar participantes
cat > analyze_working_conversation.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function analyzeWorkingConversation() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        const conversationSid = process.argv[2];
        
        console.log(`🔍 Analisando conversa: ${conversationSid}`);
        
        // Obter detalhes da conversa
        let conversation;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            conversation = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .fetch();
        } else {
            conversation = await client.conversations.v1
                .conversations(conversationSid)
                .fetch();
        }
        
        console.log(`📋 Conversa: ${conversation.friendlyName}`);
        console.log(`   SID: ${conversation.sid}`);
        console.log(`   Estado: ${conversation.state}`);
        console.log(`   Criada: ${conversation.dateCreated}`);
        console.log(`   Atualizada: ${conversation.dateUpdated}`);
        
        // Obter participantes
        let participants;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            participants = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .participants
                .list();
        } else {
            participants = await client.conversations.v1
                .conversations(conversationSid)
                .participants
                .list();
        }

        console.log(`\n👥 Total de participantes: ${participants.length}`);
        
        let whatsappParticipants = [];
        
        participants.forEach((participant, index) => {
            console.log(`\n👤 Participante ${index + 1}:`);
            console.log(`   SID: ${participant.sid}`);
            console.log(`   Identity: ${participant.identity || 'N/A'}`);
            console.log(`   Role: ${participant.roleSid || 'N/A'}`);
            console.log(`   Criado: ${participant.dateCreated}`);
            
            if (participant.messagingBinding) {
                console.log(`   📱 Messaging Binding:`);
                console.log(`      Address: ${participant.messagingBinding.address || 'N/A'}`);
                console.log(`      Proxy Address: ${participant.messagingBinding.proxyAddress || 'N/A'}`);
                console.log(`      Type: ${participant.messagingBinding.type || 'N/A'}`);
                
                if (participant.messagingBinding.address && 
                    participant.messagingBinding.address.includes('whatsapp:')) {
                    whatsappParticipants.push({
                        sid: participant.sid,
                        address: participant.messagingBinding.address,
                        proxy: participant.messagingBinding.proxyAddress,
                        created: participant.dateCreated
                    });
                }
            } else {
                console.log(`   📱 Messaging Binding: Nenhum`);
            }
        });
        
        console.log(`\n📱 Participantes WhatsApp encontrados: ${whatsappParticipants.length}`);
        
        if (whatsappParticipants.length > 0) {
            console.log(`\n🔍 ANÁLISE DOS PARTICIPANTES WHATSAPP:`);
            
            whatsappParticipants.forEach((p, index) => {
                console.log(`\n📱 WhatsApp ${index + 1}:`);
                console.log(`   SID: ${p.sid}`);
                console.log(`   Address: ${p.address}`);
                console.log(`   Proxy: ${p.proxy || 'UNDEFINED'}`);
                console.log(`   Criado: ${p.created}`);
                
                if (p.proxy) {
                    console.log(`   ✅ ESTE PARTICIPANTE TEM PROXY DEFINIDO!`);
                    console.log(`   🎯 POSSÍVEL CAUSA DO SUCESSO!`);
                } else {
                    console.log(`   ❌ Este participante não tem proxy`);
                }
            });
            
            // Identificar o participante mais antigo com proxy
            const withProxy = whatsappParticipants.filter(p => p.proxy);
            const withoutProxy = whatsappParticipants.filter(p => !p.proxy);
            
            console.log(`\n📊 RESUMO:`);
            console.log(`   ✅ Com proxy: ${withProxy.length}`);
            console.log(`   ❌ Sem proxy: ${withoutProxy.length}`);
            
            if (withProxy.length > 0) {
                console.log(`\n🎉 PARTICIPANTE(S) COM PROXY ENCONTRADO(S)!`);
                withProxy.forEach(p => {
                    console.log(`   SID: ${p.sid}`);
                    console.log(`   Proxy: ${p.proxy}`);
                    console.log(`   Criado: ${p.created}`);
                });
                
                console.log(`\n💡 HIPÓTESE:`);
                console.log(`   As mensagens funcionaram porque havia um participante`);
                console.log(`   WhatsApp com proxy address definido corretamente.`);
                
                console.log(`\n🔧 SOLUÇÃO:`);
                console.log(`   Recriar participantes usando a mesma configuração`);
                console.log(`   que funcionou para este participante.`);
            }
        }
        
        // Verificar mensagens recentes
        console.log(`\n📝 Verificando mensagens recentes...`);
        
        let messages;
        if (process.env.TWILIO_CONVERSATIONS_SERVICE_SID) {
            messages = await client.conversations.v1
                .services(process.env.TWILIO_CONVERSATIONS_SERVICE_SID)
                .conversations(conversationSid)
                .messages
                .list({ limit: 10 });
        } else {
            messages = await client.conversations.v1
                .conversations(conversationSid)
                .messages
                .list({ limit: 10 });
        }
        
        console.log(`📋 Últimas ${messages.length} mensagens:`);
        
        messages.reverse().forEach((message, index) => {
            console.log(`\n💬 Mensagem ${index + 1}:`);
            console.log(`   SID: ${message.sid}`);
            console.log(`   Author: ${message.author}`);
            console.log(`   Body: ${message.body}`);
            console.log(`   Criada: ${message.dateCreated}`);
            
            if (message.sid === 'IM0b2f4502d5144ba480f68588303203ab' || 
                message.sid === 'IMd2240095868c4b5ab9b5643649c1a38e') {
                console.log(`   🎉 ESTA É UMA DAS MENSAGENS QUE FUNCIONOU!`);
            }
        });

    } catch (error) {
        console.error('❌ Erro na análise:', error.message);
    }
}

analyzeWorkingConversation();
EOF

# Executar análise
node analyze_working_conversation.js "$CONVERSATION_SID"

# Limpar arquivo temporário
rm analyze_working_conversation.js

echo ""
echo "🔍 Verificando logs de entrega das mensagens que funcionaram..."

# Verificar se há logs de entrega para as mensagens que funcionaram
echo ""
echo "📋 Logs da mensagem 'sistema corrigido' ($MESSAGE_1):"
grep "$MESSAGE_1" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "   Nenhum log encontrado"

echo ""
echo "📋 Logs da mensagem 'API PADRÃO' ($MESSAGE_2):"
grep "$MESSAGE_2" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "   Nenhum log encontrado"

echo ""
echo "🔍 Verificando logs de entrega por timestamp..."

# Verificar logs de entrega próximos aos horários das mensagens
echo ""
echo "📋 Logs de entrega próximos a 16:57 (sistema corrigido):"
grep -E "16:5[7-9].*delivered|16:5[7-9].*sent|16:5[7-9].*failed" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "   Nenhum log encontrado"

echo ""
echo "📋 Logs de entrega próximos a 17:0[0-5] (API PADRÃO):"
grep -E "17:0[0-5].*delivered|17:0[0-5].*sent|17:0[0-5].*failed" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null || echo "   Nenhum log encontrado"

echo ""
echo "🎯 CONCLUSÃO DA ANÁLISE:"
echo "========================"
echo "Se encontrarmos participantes com proxy definido na conversa"
echo "que funcionou, isso explicará por que algumas mensagens"
echo "foram entregues com sucesso."
echo ""
echo "A solução será replicar essa configuração para todas"
echo "as novas conversas."
