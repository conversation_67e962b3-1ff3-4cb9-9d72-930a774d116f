# 🔧 Guia de Configuração do Webhook WhatsApp

## 📱 **Opção 1: WhatsApp Sandbox (Recomendado para Testes)**

### **Passo a Passo:**

1. **🌐 Acesse a Twilio Console:**
   ```
   https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
   ```

2. **📍 Navegação na Console:**
   - No menu lateral esquerdo: **Develop** → **Messaging** → **Try it out** → **Send a WhatsApp message**

3. **⚙️ Configuração do Webhook:**
   - Na seção **"Sandbox Configuration"**
   - Encontre o campo **"When a message comes in"**
   - Cole a URL: `https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming`
   - HTTP Method: **POST**

4. **💾 Salvar:**
   - Clique em **"Save Configuration"**

---

## 📱 **Opção 2: Número WhatsApp Registrado (+18382700077)**

### **Passo a Passo:**

1. **🌐 Acesse:**
   ```
   https://console.twilio.com/us1/develop/phone-numbers/manage/incoming
   ```

2. **📍 Navegação:**
   - Menu lateral: **Develop** → **Phone Numbers** → **Manage** → **Active numbers**

3. **🔍 Encontrar o Número:**
   - Procure pelo número: **+18382700077**
   - Clique no número para abrir as configurações

4. **⚙️ Configuração do Webhook:**
   - Role até a seção **"Messaging"**
   - No campo **"A message comes in"**:
     - **Webhook**: `https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming`
     - **HTTP**: **POST**

5. **💾 Salvar:**
   - Clique em **"Save configuration"** no final da página

---

## 🎯 **Qual Opção Escolher?**

### **📱 Use o Sandbox se:**
- ✅ Você quer testar rapidamente
- ✅ Não se importa com limitações
- ✅ Vai testar apenas com números pré-aprovados

### **📱 Use o Número Registrado se:**
- ✅ Quer funcionalidade completa
- ✅ Precisa receber de qualquer número
- ✅ É para uso em produção

---

## 🧪 **Como Testar Após Configurar:**

### **Para Sandbox:**
1. **📱 Envie** a mensagem de ativação para o sandbox
2. **💬 Envie** qualquer mensagem para o número do sandbox
3. **👀 Verifique** se aparece em: http://localhost:3000

### **Para Número Registrado:**
1. **📱 Envie** mensagem diretamente para: **+18382700077**
2. **👀 Verifique** se aparece em: http://localhost:3000

---

## ⚠️ **URLs Importantes:**

### **Webhook Principal:**
```
https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming
```

### **Webhook de Status (Opcional):**
```
https://e990-200-194-249-22.ngrok-free.app/webhooks/whatsapp/status
```

### **Links Diretos da Twilio Console:**

**Sandbox Configuration:**
```
https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
```

**Phone Numbers Management:**
```
https://console.twilio.com/us1/develop/phone-numbers/manage/incoming
```

**Conversations Service (se necessário):**
```
https://console.twilio.com/us1/develop/conversations/manage/services
```

---

## 🔍 **Verificação se Funcionou:**

### **1. Teste o Webhook:**
```bash
curl https://e990-200-194-249-22.ngrok-free.app/webhooks/test
```

### **2. Monitore os Logs:**
```bash
tail -f logs/twilio-2025-06-04.log
```

### **3. Envie Mensagem de Teste:**
- 📱 Envie mensagem WhatsApp para o número configurado
- 👀 Veja se aparece nos logs e na interface

---

## 🎉 **Resultado Esperado:**

Após configurar corretamente:

1. **📱 Você envia** mensagem WhatsApp para o número
2. **🔄 Webhook recebe** a mensagem
3. **📋 Aparece nos logs** do sistema
4. **🌐 Aparece na interface** web (http://localhost:3000)
5. **💬 Você pode responder** através da interface

---

## 🆘 **Se Não Funcionar:**

### **Verificações:**
1. ✅ ngrok está rodando?
2. ✅ URL do webhook está correta?
3. ✅ Método HTTP é POST?
4. ✅ Número está correto?

### **Debug:**
```bash
# Verificar ngrok
curl -s http://localhost:4040/api/tunnels | jq

# Verificar logs
tail -f logs/twilio-2025-06-04.log

# Testar webhook
curl https://e990-200-194-249-22.ngrok-free.app/webhooks/test
```

---

## 📞 **Contatos de Suporte:**

Se precisar de ajuda:
- 📧 Twilio Support: https://support.twilio.com
- 📚 Documentação: https://www.twilio.com/docs/whatsapp
- 🎯 Seu sistema: http://localhost:3000
