# 🚀 Twilio WhatsApp Chat System

Este projeto é um sistema de chat WhatsApp integrado com Twilio Conversations API. O sistema funciona de forma **reativa**, aguardando mensagens dos usuários finais para iniciar conversas automaticamente, sem necessidade de templates ou configuração prévia.

## 📋 Funcionalidades

- ✅ **Sistema Reativo**: Aguarda mensagens dos usuários para iniciar conversas
- ✅ **WhatsApp Sender Registrado**: Usa número registrado (+***********) sem limitações
- ✅ **Conversas Automáticas**: Cria conversas automaticamente quando recebe primeira mensagem
- ✅ **Interface de Chat**: Interface web para operadores responderem mensagens
- ✅ **Typing Indicator**: Indicador de digitação bidirecional
- ✅ **Logs em Tempo Real**: Monitoramento completo de eventos
- ✅ **Webhooks Configurados**: Recebe notificações via ngrok tunnel

## 🛠️ Tecnologias Utilizadas

- **Backend**: Node.js, Express.js
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **API**: Twilio Conversations SDK
- **Webhooks**: ngrok para exposição local
- **Styling**: CSS Grid, Flexbox, Animações CSS

## 📦 Instalação e Configuração

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd twiliowhats
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas credenciais da Twilio (veja seção de configuração abaixo).

### 4. Configure o ngrok (necessário para webhooks)
```bash
# Instalar ngrok (Mac)
brew install ngrok

# Ou baixe em: https://ngrok.com/download
```

### 5. Execute o sistema reativo

#### 🍎 Para usuários Mac (Recomendado):
```bash
# Inicialização completa do sistema reativo
./setup-reactive-ngrok.sh

# Desenvolvimento com auto-reload
./dev-mac.sh

# Executar todos os testes
./test-all.sh
```

#### 💻 Comandos tradicionais:
```bash
# Desenvolvimento (com nodemon)
npm run dev

# Produção
npm start

# Testes do sistema reativo
npm run test:reactive
```

### 6. Acesse a aplicação
Abra seu navegador em: `http://localhost:3000`

## ⚙️ Configuração da Twilio

Antes de usar a aplicação, você precisa configurar alguns recursos na Twilio. Consulte o arquivo `docs/TWILIO_SETUP.md` para instruções detalhadas.

### Variáveis de Ambiente Necessárias

```env
# Credenciais básicas da Twilio
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here

# Credenciais da API (para gerar tokens)
TWILIO_API_KEY=your_api_key_here
TWILIO_API_SECRET=your_api_secret_here

# Service SID do Conversations (opcional, mas recomendado)
TWILIO_CONVERSATIONS_SERVICE_SID=your_service_sid_here

# Configurações do WhatsApp Sandbox
TWILIO_WHATSAPP_SANDBOX_NUMBER=whatsapp:+***********
TWILIO_SANDBOX_KEYWORD=your_sandbox_keyword_here

# Configurações do servidor
PORT=3000
NODE_ENV=development
```

## 🚀 Como Usar o Sistema Reativo

### 1. Configurar Webhooks na Twilio
Após executar `./setup-reactive-ngrok.sh`, configure os webhooks na Twilio Console:

#### WhatsApp Sender:
- Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
- Encontre: Sender +***********
- Configure as URLs geradas pelo script

#### Conversations Service:
- Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
- Configure webhook para eventos: onMessageAdded, onTypingStarted, onTypingEnded

### 2. Usar a Interface de Operador
1. **Conectar**: Clique em "Conectar como Operador"
2. **Aguardar**: O sistema fica "de prontidão" aguardando mensagens
3. **Receber**: Quando um usuário envia mensagem, conversa aparece automaticamente
4. **Responder**: Clique na conversa para abrir o chat e responder
5. **Typing Indicator**: Digite para mostrar que está respondendo

### 3. Fluxo do Sistema Reativo
1. **Usuário envia mensagem** para +*********** via WhatsApp
2. **Sistema recebe webhook** e cria conversa automaticamente
3. **Operador é notificado** na interface web
4. **Operador responde** através da interface
5. **Usuário recebe resposta** no WhatsApp
6. **Typing indicators** funcionam bidirecionalmente

## 📁 Estrutura do Projeto

```
twiliowhats/
├── public/                    # Frontend (Interface de Operador)
│   ├── index.html            # Interface reativa principal
│   ├── style.css             # Estilos responsivos
│   └── script.js             # Lógica do sistema reativo
├── routes/                   # Rotas da API
│   ├── auth.js              # Autenticação e tokens
│   ├── conversations.js     # Gerenciamento de conversas
│   └── webhooks.js          # Webhooks para receber mensagens
├── utils/                   # Utilitários
│   └── logger.js           # Sistema de logs
├── old/                     # Arquivos do sistema anterior
│   ├── test-conversation.sh
│   └── test-intelligent-conversation.sh
├── logs/                    # Logs do sistema
├── docs/                    # Documentação
├── server.js               # Servidor principal
├── setup-reactive-ngrok.sh # Configuração automática ngrok
├── test-reactive-system.sh # Testes do sistema reativo
├── package.json            # Dependências
├── .env                    # Variáveis de ambiente
└── README.md               # Este arquivo
```

## 🔧 API Endpoints

### Autenticação
- `POST /api/auth/token` - Gerar token de acesso para operador
- `GET /api/auth/verify` - Verificar configuração da Twilio

### Conversas (Sistema Reativo)
- `GET /api/conversations` - Listar conversas ativas
- `POST /api/conversations/:sid/participants/chat` - Adicionar operador à conversa
- `POST /api/conversations/:sid/messages` - Enviar mensagem
- `DELETE /api/conversations/cleanup-whatsapp/:number` - Limpar participante (teste)

### Webhooks (Recebimento Automático)
- `POST /webhooks/whatsapp/incoming` - Receber mensagens WhatsApp
- `POST /webhooks/whatsapp/status` - Status de mensagens
- `POST /webhooks/conversations` - Eventos de conversas
- `GET /webhooks/test` - Testar webhooks
- `GET /webhooks/logs` - Ver logs recentes

## 🐛 Troubleshooting

### Problemas Comuns

1. **Erro de conexão**
   - Verifique se as credenciais estão corretas
   - Confirme se o Account SID e Auth Token estão válidos

2. **Token inválido**
   - Verifique se API Key e API Secret estão configurados
   - Confirme se o Service SID está correto

3. **WhatsApp não recebe mensagens**
   - Confirme se o número está conectado ao sandbox
   - Verifique se enviou a mensagem "join [keyword]"
   - Confirme se o número está no formato correto (+*************)

4. **Typing indicator não funciona**
   - Confirme se ambos os participantes estão conectados
   - Verifique se a conversa está ativa
   - Teste com diferentes navegadores

### Logs e Debug

A aplicação possui logs detalhados na interface web. Use a seção "Logs de Eventos" para acompanhar:
- Conexões
- Criação de conversas
- Adição de participantes
- Envio de mensagens
- Eventos de typing

## 📚 Recursos Adicionais

- [Documentação Twilio Conversations](https://www.twilio.com/docs/conversations)
- [Typing Indicator API](https://www.twilio.com/docs/conversations/typing-indicator)
- [WhatsApp com Conversations](https://www.twilio.com/docs/conversations/using-whatsapp-conversations)
- [Twilio Sandbox para WhatsApp](https://www.twilio.com/docs/whatsapp/sandbox)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Se você encontrar problemas ou tiver dúvidas:

1. Consulte a documentação da Twilio
2. Verifique os logs da aplicação
3. Abra uma issue no repositório
4. Entre em contato com o suporte da Twilio

---

**Nota**: Este projeto é para fins de teste e desenvolvimento. Para uso em produção, implemente autenticação adequada, validação de entrada e outras medidas de segurança necessárias.
