#!/bin/bash

# Script para corrigir o problema do WhatsApp Sender
# Remove conversas antigas criadas com sandbox e força uso do número registrado

echo "🔧 Corrigindo Configuração do WhatsApp Sender"
echo "============================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Verificar configuração atual
echo ""
echo "🔍 Verificando configuração atual..."

# Verificar .env
if grep -q "TWILIO_WHATSAPP_SENDER_NUMBER=whatsapp:+***********" .env; then
    echo "✅ WhatsApp Sender configurado corretamente no .env: +***********"
else
    echo "❌ WhatsApp Sender não configurado corretamente no .env"
    echo "📝 Configure: TWILIO_WHATSAPP_SENDER_NUMBER=whatsapp:+***********"
    exit 1
fi

# Número do cliente para teste
CLIENT_NUMBER="+5551993590210"
echo "📱 Número do cliente para teste: $CLIENT_NUMBER"

echo ""
echo "🧹 Limpando configuração antiga..."

# Limpar participante WhatsApp da conversa existente
echo "🔄 Removendo participante WhatsApp antigo..."
curl -s -X DELETE "http://localhost:3000/api/conversations/cleanup-whatsapp/$CLIENT_NUMBER" || true

echo ""
echo "⏳ Aguardando 3 segundos..."
sleep 3

echo ""
echo "🔄 Testando criação de nova conversa..."

# Criar nova conversa com configuração correta
echo "📝 Criando nova conversa com WhatsApp Sender registrado..."

# Simular webhook de mensagem recebida para forçar criação automática
echo "📱 Simulando mensagem recebida do cliente..."

# Fazer chamada para webhook simulando mensagem do WhatsApp
curl -s -X POST http://localhost:3000/webhooks/whatsapp/incoming \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:$CLIENT_NUMBER" \
  -d "To=whatsapp:+***********" \
  -d "Body=Teste de correção" \
  -d "MessageSid=TEST_$(date +%s)" \
  -d "AccountSid=TEST" \
  -d "ApiVersion=2010-04-01" > /dev/null

echo "✅ Webhook simulado enviado"

echo ""
echo "⏳ Aguardando processamento..."
sleep 5

echo ""
echo "🔍 Verificando conversas ativas..."

# Listar conversas
CONVERSATIONS=$(curl -s http://localhost:3000/api/conversations)
echo "📋 Conversas encontradas:"
echo "$CONVERSATIONS" | jq '.' 2>/dev/null || echo "$CONVERSATIONS"

echo ""
echo "🧪 Testando envio de mensagem..."

# Obter SID da conversa mais recente
CONVERSATION_SID=$(echo "$CONVERSATIONS" | jq -r '.conversations[0].sid' 2>/dev/null)

if [ "$CONVERSATION_SID" != "null" ] && [ ! -z "$CONVERSATION_SID" ]; then
    echo "📝 Usando conversa: $CONVERSATION_SID"
    
    # Adicionar operador à conversa
    echo "👤 Adicionando operador à conversa..."
    curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/participants/chat" \
      -H "Content-Type: application/json" \
      -d '{"identity":"operador-teste"}' > /dev/null
    
    echo "⏳ Aguardando 2 segundos..."
    sleep 2
    
    # Enviar mensagem de teste
    echo "💬 Enviando mensagem de teste..."
    SEND_RESULT=$(curl -s -X POST "http://localhost:3000/api/conversations/$CONVERSATION_SID/messages" \
      -H "Content-Type: application/json" \
      -d '{"body":"Teste de correção - mensagem do operador","author":"operador-teste"}')
    
    echo "📤 Resultado do envio:"
    echo "$SEND_RESULT" | jq '.' 2>/dev/null || echo "$SEND_RESULT"
    
    # Verificar se a mensagem foi enviada com sucesso
    if echo "$SEND_RESULT" | grep -q '"messageSid"'; then
        echo ""
        echo "🎉 SUCESSO! Mensagem enviada com WhatsApp Sender registrado"
        echo "✅ Problema corrigido"
        
        echo ""
        echo "📊 Verificando logs de entrega..."
        sleep 3
        
        # Verificar logs recentes
        echo "📝 Logs recentes:"
        tail -5 logs/twilio-$(date +%Y-%m-%d).log
        
    else
        echo ""
        echo "❌ FALHA! Mensagem não foi enviada"
        echo "🔍 Verifique os logs para mais detalhes"
    fi
    
else
    echo "❌ Nenhuma conversa encontrada"
    echo "🔄 Tente enviar uma mensagem WhatsApp para +*********** primeiro"
fi

echo ""
echo "📋 Instruções para Teste Manual:"
echo "================================"
echo "1. 📱 Envie uma mensagem WhatsApp para: +***********"
echo "2. 🌐 Acesse: http://localhost:3000"
echo "3. 👨‍💼 Conecte como operador"
echo "4. 💬 Responda a mensagem através da interface"
echo "5. 📱 Verifique se a resposta chega no WhatsApp"

echo ""
echo "🔍 Para monitorar logs em tempo real:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"

echo ""
echo "🔧 Correção concluída!"
