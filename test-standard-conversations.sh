#!/bin/bash

# Script para testar API padrão do Conversations (sem Service)
# Algumas funcionalidades podem funcionar melhor sem Conversations Service

echo "🔧 TESTE - API Padrão do Conversations (sem Service)"
echo "===================================================="

# Verificar se o servidor está rodando
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Servidor não está rodando. Inicie com: npm start"
    exit 1
fi

echo "✅ Servidor está rodando"

# Configurações
CLIENT_NUMBER="+*************"

echo "📱 Cliente: $CLIENT_NUMBER"

echo ""
echo "🧪 Testando criação de conversa e participante sem Conversations Service..."

# Criar script Node.js para testar sem Service
cat > test_standard_api.js << 'EOF'
const twilio = require('twilio');
require('dotenv').config();

async function testStandardAPI() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        
        const clientNumber = process.argv[2];
        const formattedNumber = clientNumber.startsWith('whatsapp:') ? clientNumber : `whatsapp:${clientNumber}`;
        const senderNumber = process.env.TWILIO_WHATSAPP_SENDER_NUMBER;
        
        console.log(`🧪 Testando API padrão do Conversations:`);
        console.log(`   Address: ${formattedNumber}`);
        console.log(`   Proxy: ${senderNumber}`);
        console.log(`   Usando: API padrão (sem Conversations Service)`);
        
        // 1. Criar nova conversa usando API padrão
        console.log(`\n📝 Criando nova conversa...`);
        const conversation = await client.conversations.v1
            .conversations
            .create({
                friendlyName: `Teste API Padrão - ${new Date().toLocaleString()}`
            });
        
        console.log(`✅ Conversa criada: ${conversation.sid}`);
        
        // 2. Adicionar participante WhatsApp usando API padrão
        console.log(`\n👤 Adicionando participante WhatsApp...`);
        const participant = await client.conversations.v1
            .conversations(conversation.sid)
            .participants
            .create({
                'messagingBinding.address': formattedNumber,
                'messagingBinding.proxyAddress': senderNumber
            });

        console.log(`✅ Participante criado com sucesso:`);
        console.log(`   SID: ${participant.sid}`);
        console.log(`   Address: ${participant.messagingBinding?.address}`);
        console.log(`   Proxy Address: ${participant.messagingBinding?.proxyAddress}`);
        console.log(`   Type: ${participant.messagingBinding?.type}`);
        
        // Verificar se proxy foi definido
        if (participant.messagingBinding?.proxyAddress) {
            console.log(`\n🎉 SUCESSO! Proxy Address definido com API padrão!`);
            console.log(`RESULT=SUCCESS`);
            console.log(`CONVERSATION_SID=${conversation.sid}`);
            console.log(`PARTICIPANT_SID=${participant.sid}`);
            console.log(`PROXY_ADDRESS=${participant.messagingBinding.proxyAddress}`);
        } else {
            console.log(`\n❌ Proxy Address ainda não foi definido`);
            console.log(`RESULT=FAILED`);
            console.log(`CONVERSATION_SID=${conversation.sid}`);
            console.log(`PARTICIPANT_SID=${participant.sid}`);
            console.log(`PROXY_ADDRESS=N/A`);
        }
        
        // 3. Adicionar operador à conversa
        console.log(`\n👨‍💼 Adicionando operador...`);
        const operator = await client.conversations.v1
            .conversations(conversation.sid)
            .participants
            .create({
                identity: 'operador-api-padrao'
            });
        
        console.log(`✅ Operador adicionado: ${operator.sid}`);
        
        // 4. Enviar mensagem de teste
        console.log(`\n💬 Enviando mensagem de teste...`);
        const message = await client.conversations.v1
            .conversations(conversation.sid)
            .messages
            .create({
                body: '🧪 TESTE COM API PADRÃO - Esta mensagem deve chegar no WhatsApp!',
                author: 'operador-api-padrao'
            });
        
        console.log(`✅ Mensagem enviada: ${message.sid}`);
        console.log(`MESSAGE_SID=${message.sid}`);

    } catch (error) {
        console.error(`❌ Erro na API padrão: ${error.message}`);
        console.error(`   Code: ${error.code}`);
        console.log(`RESULT=ERROR`);
        console.log(`ERROR_CODE=${error.code}`);
        console.log(`ERROR_MESSAGE=${error.message}`);
    }
}

testStandardAPI();
EOF

# Executar teste
TEST_OUTPUT=$(node test_standard_api.js "$CLIENT_NUMBER")
TEST_RESULT=$?

# Limpar arquivo temporário
rm test_standard_api.js

echo "$TEST_OUTPUT"

# Extrair resultado
RESULT=$(echo "$TEST_OUTPUT" | grep "RESULT=" | cut -d'=' -f2)
CONVERSATION_SID=$(echo "$TEST_OUTPUT" | grep "CONVERSATION_SID=" | cut -d'=' -f2)
PARTICIPANT_SID=$(echo "$TEST_OUTPUT" | grep "PARTICIPANT_SID=" | cut -d'=' -f2)
PROXY_ADDRESS=$(echo "$TEST_OUTPUT" | grep "PROXY_ADDRESS=" | cut -d'=' -f2)
MESSAGE_SID=$(echo "$TEST_OUTPUT" | grep "MESSAGE_SID=" | cut -d'=' -f2)

echo ""
echo "📋 Resultado do Teste com API Padrão:"
echo "====================================="
echo "🎯 Status: $RESULT"
echo "💬 Conversation SID: $CONVERSATION_SID"
echo "👤 Participant SID: $PARTICIPANT_SID"
echo "📨 Proxy Address: $PROXY_ADDRESS"
echo "💬 Message SID: $MESSAGE_SID"

if [ "$RESULT" = "SUCCESS" ]; then
    echo ""
    echo "🎉 SUCESSO! API padrão funcionou!"
    echo ""
    echo "⏳ Monitorando entrega da mensagem por 30 segundos..."
    
    # Monitorar logs
    for i in {1..30}; do
        sleep 1
        
        # Verificar logs específicos da mensagem
        if [ ! -z "$MESSAGE_SID" ] && grep -q "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null; then
            echo ""
            echo "📋 Logs da mensagem encontrados:"
            grep "$MESSAGE_SID" logs/twilio-$(date +%Y-%m-%d).log | tail -3
            break
        fi
        
        # Verificar logs de entrega recentes
        RECENT_LOG=$(tail -5 logs/twilio-$(date +%Y-%m-%d).log 2>/dev/null | grep -E "(delivered|sent|failed)" | tail -1)
        if [[ "$RECENT_LOG" == *"$(date +%Y-%m-%d)"* ]]; then
            TIMESTAMP=$(echo "$RECENT_LOG" | jq -r '.timestamp' 2>/dev/null)
            if [[ "$TIMESTAMP" > "$(date -u -v-60s +%Y-%m-%dT%H:%M:%S)" ]]; then
                echo ""
                echo "📱 Log de entrega recente:"
                echo "$RECENT_LOG" | jq '.' 2>/dev/null || echo "$RECENT_LOG"
                break
            fi
        fi
        
        echo -n "."
    done
    
    echo ""
    echo ""
    echo "📋 SOLUÇÃO ENCONTRADA:"
    echo "====================="
    echo "✅ API padrão do Conversations funciona!"
    echo "✅ Proxy Address definido corretamente"
    echo "✅ Mensagem enviada com sucesso"
    echo ""
    echo "🔧 RECOMENDAÇÃO:"
    echo "================"
    echo "1. 🔄 Considere usar API padrão em vez de Conversations Service"
    echo "2. 📝 Atualize o código para usar API padrão quando necessário"
    echo "3. 🧪 Teste a nova conversa: $CONVERSATION_SID"
    echo ""
    echo "🎯 TESTE MANUAL:"
    echo "==============="
    echo "1. 📱 Verifique se a mensagem chegou no WhatsApp ($CLIENT_NUMBER)"
    echo "2. 📱 Responda no WhatsApp"
    echo "3. 🌐 Use a interface web para monitorar"
    
elif [ "$RESULT" = "ERROR" ]; then
    ERROR_CODE=$(echo "$TEST_OUTPUT" | grep "ERROR_CODE=" | cut -d'=' -f2)
    ERROR_MESSAGE=$(echo "$TEST_OUTPUT" | grep "ERROR_MESSAGE=" | cut -d'=' -f2)
    
    echo ""
    echo "❌ Teste com API padrão também falhou"
    echo "🔍 Error Code: $ERROR_CODE"
    echo "📝 Error Message: $ERROR_MESSAGE"
    echo ""
    echo "🔧 Próximas ações:"
    echo "=================="
    echo "1. 📞 Contatar suporte Twilio"
    echo "2. 🔍 Verificar configuração da conta"
    echo "3. 📋 Mencionar problema com proxyAddress"
    
else
    echo ""
    echo "❌ Resultado inesperado: $RESULT"
fi

echo ""
echo "🔍 Para monitorar logs:"
echo "tail -f logs/twilio-$(date +%Y-%m-%d).log"
