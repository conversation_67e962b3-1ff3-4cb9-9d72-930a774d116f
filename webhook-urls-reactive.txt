# URLs dos Webhooks para Sistema Reativo
# Gerado em: Wed Jun  4 13:42:29 -03 2025

T<PERSON>el ngrok: https://5cb0-200-194-249-22.ngrok-free.app

## Para WhatsApp Sender (+18382700077):
Webhook URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming
Status callback URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/whatsapp/status

## Para Conversations Service:
Webhook URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/conversations

## Configuração na Twilio Console:

### 1. WhatsApp Sender:
   - Acesse: https://console.twilio.com/us1/develop/sms/senders/whatsapp
   - Encontre: Sender +18382700077
   - Configure:
     * Webhook URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/whatsapp/incoming
     * Status callback URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/whatsapp/status
     * HTTP Method: POST

### 2. Conversations Service:
   - Acesse: https://console.twilio.com/us1/develop/conversations/manage/services
   - Selecione: Seu Conversations Service
   - Configure:
     * Webhook URL: https://5cb0-200-194-249-22.ngrok-free.app/webhooks/conversations
     * HTTP Method: POST
     * Events: onMessageAdded, onTypingStarted, onTypingEnded

## Teste:
curl https://5cb0-200-194-249-22.ngrok-free.app/webhooks/test
